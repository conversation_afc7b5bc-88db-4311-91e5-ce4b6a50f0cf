# Fortschrittsbalken-Tasten-Wechsler

Diese Komponente erweitert den Makro-Recorder um eine Funktion, die automatisch zwischen den Tasten A und D wechselt, basierend auf dem Fortschritt eines Balkens.

## Funktionsweise

1. Die Komponente erkennt einen Fortschrittsbalken auf dem Bildschirm
2. Sie überwacht kontinuierlich den Fortschritt des Balkens
3. <PERSON><PERSON> der Balken sich nach unten bewegt, wird die aktuell gedrückte Taste (A oder D) beibehalten
4. Wenn der Balken sich nicht mehr bewegt, wechselt die Komponente zur anderen Taste
5. Dieser Prozess wird wieder<PERSON>t, bis der Balken das Ziel erreicht hat

## Verwendung

1. Starte das Programm mit `python progress_bar_key_switcher.py`
2. Definiere den Bereich des Fortschrittsbalkens auf dem Bildschirm
3. Starte die Überwachung
4. Das Programm wechselt automatisch zwischen den Tasten A und D, um den Fortschrittsbalken zu füllen

## Integration mit dem Makro-Recorder

Diese Komponente kann entweder eigenständig oder als Teil des Makro-Recorders verwendet werden. Die Integration erfolgt über die `ProgressBarKeySwitcher`-Klasse, die in den Makro-Recorder eingebunden werden kann.
