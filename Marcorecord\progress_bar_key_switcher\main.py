import tkinter as tk
from progress_bar_key_switcher import ProgressBarKeySwitcher

def main():
    """
    Haupteinstiegspunkt für den Fortschrittsbalken-Tasten-Wechsler.
    Initialisiert die UI und startet die Anwendung.
    """
    # Hauptfenster erstellen
    root = tk.Tk()
    
    # Fortschrittsbalken-Tasten-Wechsler initialisieren
    app = ProgressBarKeySwitcher(root)
    
    # Hauptschleife starten
    root.mainloop()

if __name__ == "__main__":
    main()
