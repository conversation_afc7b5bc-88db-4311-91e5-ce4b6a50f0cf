import tkinter as tk
from tkinter import ttk
import sys
import os

# Pfad zum übergeordneten Verzeichnis hinzufügen, um den Makro-Recorder zu importieren
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from macro_recorder import MacroRecorder
from progress_bar_key_switcher import ProgressBarKeySwitcher

class IntegratedMacroRecorder(MacroRecorder):
    def __init__(self, root):
        """
        Erweitert den Makro-Recorder um den Fortschrittsbalken-Tasten-Wechsler.
        
        Args:
            root: Das Tkinter-Root-Fenster.
        """
        # Makro-Recorder initialisieren
        super().__init__(root)
        
        # Tab für den Fortschrittsbalken-Tasten-Wechsler hinzufügen
        self.progress_bar_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.progress_bar_tab, text="Fortschrittsbalken-Wechsler")
        
        # Fortschrittsbalken-Tasten-Wechsler initialisieren
        self.progress_bar_key_switcher = ProgressBarKeySwitcher(self.progress_bar_tab)
        
        # Log-Funktion überschreiben, um Logs auch im Makro-Recorder anzuzeigen
        original_log_function = self.progress_bar_key_switcher.log
        
        def integrated_log(message):
            # Original-Log-Funktion aufrufen
            original_log_function(message)
            
            # Auch im Makro-Recorder loggen
            self.log(f"[Fortschrittsbalken] {message}")
        
        # Log-Funktion ersetzen
        self.progress_bar_key_switcher.log = integrated_log

def main():
    """
    Haupteinstiegspunkt für den integrierten Makro-Recorder.
    Initialisiert die UI und startet die Anwendung.
    """
    # Hauptfenster erstellen
    root = tk.Tk()
    
    # Integrierten Makro-Recorder initialisieren
    app = IntegratedMacroRecorder(root)
    
    # Hauptschleife starten
    root.mainloop()

if __name__ == "__main__":
    main()
