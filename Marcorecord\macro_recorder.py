import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import cv2
import numpy as np
import pyautogui
import time
import os
import threading
import pickle
import base64
import json
from PIL import Image, ImageTk
from datetime import datetime

class MacroRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("Makro-Recorder")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # Mindestgröße festlegen, damit der Log-Bereich immer sichtbar ist
        self.root.minsize(800, 600)

        # Gemeinsame Variablen
        self.screenshot = None  # (path, image, cv2_image)
        self.monitoring_active = False
        self.monitoring_thread = None

        # Objekterkennung Variablen
        # Liste von Schritten: [{"name": name, "key": key, "confidence": confidence, "rate": rate, "active": active, "screenshot": (path, image, cv2_image), "region": (x, y, w, h)}]
        self.steps = []
        self.current_step_id = -1

        # UI erstellen
        self.create_ui()

        # Ordner für Referenzbilder erstellen
        if not os.path.exists("reference_images"):
            os.makedirs("reference_images")

    def create_ui(self):
        """Erstellt die Benutzeroberfläche"""
        # Hauptframe
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Notebook für Tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=5)

        # Tab 1: Objekterkennung (Schritte)
        self.detection_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.detection_tab, text="Objekterkennung")

        # Tab 2: Einstellungen
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="Einstellungen")

        # Gemeinsame Steuerelemente
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        # Start/Stop-Button
        self.start_stop_button = ttk.Button(control_frame, text="Überwachung starten",
                                          command=self.toggle_monitoring)
        self.start_stop_button.pack(side=tk.LEFT, padx=5)

        # Speichern/Laden-Buttons
        ttk.Button(control_frame, text="Konfiguration speichern",
                  command=self.save_configuration).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="Schnellspeichern",
                  command=self.quick_save).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="Konfiguration laden",
                  command=self.load_configuration).pack(side=tk.LEFT, padx=5)

        # Status-Anzeige
        self.status_var = tk.StringVar(value="Bereit")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Log-Bereich mit fester Höhe und Scrollbar
        log_frame = ttk.LabelFrame(main_frame, text="Log")
        log_frame.pack(fill=tk.X, pady=5)

        # Container für Text und Scrollbar
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        # Scrollbar für Log
        log_scrollbar = ttk.Scrollbar(log_container)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Text-Widget mit fester Höhe (reduziert auf 3 Zeilen für kleinere Bildschirme)
        self.log_text = tk.Text(log_container, height=3, wrap=tk.WORD, yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar mit Text verbinden
        log_scrollbar.config(command=self.log_text.yview)

        # Objekterkennung Tab einrichten
        self.setup_detection_tab()

        # Einstellungen Tab einrichten
        self.setup_settings_tab()

    def setup_detection_tab(self):
        """Richtet den Objekterkennung-Tab ein"""
        # Hauptframe für den Tab
        detection_frame = ttk.Frame(self.detection_tab, padding="10")
        detection_frame.pack(fill=tk.BOTH, expand=True)

        # Linke Seite: Steuerelemente mit Scrollbar
        left_outer_frame = ttk.Frame(detection_frame)
        left_outer_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Scrollbar für die linke Seite
        left_scrollbar = ttk.Scrollbar(left_outer_frame)
        left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Canvas für scrollbaren Inhalt
        left_canvas = tk.Canvas(left_outer_frame, width=300, yscrollcommand=left_scrollbar.set)
        left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbar mit Canvas verbinden
        left_scrollbar.config(command=left_canvas.yview)

        # Frame innerhalb des Canvas für die eigentlichen Inhalte
        left_frame = ttk.Frame(left_canvas)
        left_canvas.create_window((0, 0), window=left_frame, anchor=tk.NW, width=280)

        # Schritte-Bereich
        steps_frame = ttk.LabelFrame(left_frame, text="Schritte", padding="10")
        steps_frame.pack(fill=tk.X, pady=5)

        # Funktion zum Aktualisieren der Scrollregion
        def update_scrollregion(event):
            left_canvas.configure(scrollregion=left_canvas.bbox("all"))

        left_frame.bind("<Configure>", update_scrollregion)

        ttk.Button(steps_frame, text="Neuen Schritt hinzufügen",
                  command=self.add_step).pack(fill=tk.X, pady=5)

        ttk.Button(steps_frame, text="Ausgewählten Schritt bearbeiten",
                  command=self.edit_selected_step).pack(fill=tk.X, pady=5)

        ttk.Button(steps_frame, text="Ausgewählten Schritt entfernen",
                  command=self.remove_selected_step).pack(fill=tk.X, pady=5)

        # Screenshot-Bereich (nach den Schritten)
        screenshot_frame = ttk.LabelFrame(left_frame, text="Screenshot für Schritt", padding="10")
        screenshot_frame.pack(fill=tk.X, pady=5)

        ttk.Button(screenshot_frame, text="Screenshot aufnehmen",
                  command=self.take_screenshot_for_step).pack(fill=tk.X, pady=5)

        ttk.Button(screenshot_frame, text="Screenshot laden",
                  command=self.upload_screenshot_for_step).pack(fill=tk.X, pady=5)

        # Treeview für Schritte
        self.steps_tree = ttk.Treeview(steps_frame, columns=("Name", "Region", "Taste", "Konfidenz", "Rate", "Status"), show="headings")
        self.steps_tree.heading("Name", text="Name")
        self.steps_tree.heading("Region", text="Region")
        self.steps_tree.heading("Taste", text="Taste")
        self.steps_tree.heading("Konfidenz", text="Konfidenz")
        self.steps_tree.heading("Rate", text="Rate")
        self.steps_tree.heading("Status", text="Status")
        self.steps_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # Scrollbar für Treeview
        steps_scrollbar = ttk.Scrollbar(self.steps_tree, command=self.steps_tree.yview)
        steps_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.steps_tree.config(yscrollcommand=steps_scrollbar.set)

        # Treeview-Event für Schrittwechsel
        self.steps_tree.bind("<<TreeviewSelect>>", self.on_step_selected)

        # Einstellungen-Bereich
        settings_frame = ttk.LabelFrame(left_frame, text="Schritt-Einstellungen", padding="10")
        settings_frame.pack(fill=tk.X, pady=5)

        # Taste für den Schritt
        key_frame = ttk.Frame(settings_frame)
        key_frame.pack(fill=tk.X, pady=5)

        ttk.Label(key_frame, text="Taste:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.step_key_var = tk.StringVar(value="")
        self.step_key_entry = ttk.Entry(key_frame, textvariable=self.step_key_var, width=5)
        self.step_key_entry.grid(row=0, column=1, padx=5, pady=5)
        self.step_key_entry.bind("<KeyRelease>", self.update_step_key)

        # Konfidenz für den Schritt
        ttk.Label(settings_frame, text="Konfidenz:").pack(anchor=tk.W)
        step_confidence_frame = ttk.Frame(settings_frame)
        step_confidence_frame.pack(fill=tk.X, pady=5)
        self.step_confidence_scale = ttk.Scale(step_confidence_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL,
                                        command=self.update_step_confidence)
        self.step_confidence_scale.set(0.7)  # Default confidence threshold
        self.step_confidence_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.step_confidence_label = ttk.Label(step_confidence_frame, text="0.70")
        self.step_confidence_label.pack(side=tk.RIGHT, padx=5)

        # Prüfrate für den Schritt
        ttk.Label(settings_frame, text="Prüfrate (s):").pack(anchor=tk.W)
        step_rate_frame = ttk.Frame(settings_frame)
        step_rate_frame.pack(fill=tk.X, pady=5)
        self.step_rate_scale = ttk.Scale(step_rate_frame, from_=0.01, to=1.0, orient=tk.HORIZONTAL,
                                        command=self.update_step_rate)
        self.step_rate_scale.set(0.1)
        self.step_rate_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.step_rate_label = ttk.Label(step_rate_frame, text="0.10")
        self.step_rate_label.pack(side=tk.RIGHT, padx=5)

        # Rechte Seite: Screenshot und Vorschau
        right_frame = ttk.Frame(detection_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Screenshot-Anzeige
        screenshot_display_frame = ttk.LabelFrame(right_frame, text="Screenshot")
        screenshot_display_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Canvas für Screenshot mit Scrollbars
        self.detection_canvas_frame = ttk.Frame(screenshot_display_frame)
        self.detection_canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars für Canvas
        h_scrollbar = ttk.Scrollbar(self.detection_canvas_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(self.detection_canvas_frame, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Canvas mit Scrollbars verbinden
        self.detection_canvas = tk.Canvas(self.detection_canvas_frame, bg="white",
                                        xscrollcommand=h_scrollbar.set,
                                        yscrollcommand=v_scrollbar.set)
        self.detection_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbars mit Canvas verbinden
        h_scrollbar.config(command=self.detection_canvas.xview)
        v_scrollbar.config(command=self.detection_canvas.yview)

        # Canvas-Events
        self.detection_canvas.bind("<ButtonPress-1>", self.on_detection_canvas_press)
        self.detection_canvas.bind("<B1-Motion>", self.on_detection_canvas_drag)
        self.detection_canvas.bind("<ButtonRelease-1>", self.on_detection_canvas_release)

        # Screenshot-Info
        self.detection_screenshot_info_var = tk.StringVar(value="Kein Screenshot geladen")
        ttk.Label(screenshot_display_frame, textvariable=self.detection_screenshot_info_var).pack(anchor=tk.W)

        # Überwachung starten
        control_frame = ttk.LabelFrame(left_frame, text="Steuerung", padding="10")
        control_frame.pack(fill=tk.X, pady=5)

        self.start_stop_button_tab = ttk.Button(control_frame, text="Überwachung starten",
                                         command=self.toggle_monitoring)
        self.start_stop_button_tab.pack(fill=tk.X, pady=5)

    def setup_settings_tab(self):
        """Richtet den Tab für die Einstellungen ein"""
        # Hauptframe für den Einstellungen-Tab
        settings_frame = ttk.Frame(self.settings_tab, padding="10")
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # Überschrift
        ttk.Label(settings_frame, text="Einstellungen speichern und laden",
                 font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))

        # Speichern/Laden-Bereich
        save_load_frame = ttk.LabelFrame(settings_frame, text="Speichern und Laden", padding="10")
        save_load_frame.pack(fill=tk.X, pady=5)

        # Speichern-Button
        ttk.Button(save_load_frame, text="Konfiguration speichern",
                  command=self.save_configuration).pack(fill=tk.X, pady=5)

        # Schnellspeichern-Button
        ttk.Button(save_load_frame, text="Schnellspeichern",
                  command=self.quick_save).pack(fill=tk.X, pady=5)

        # Laden-Button
        ttk.Button(save_load_frame, text="Konfiguration laden",
                  command=self.load_configuration).pack(fill=tk.X, pady=5)

        # Speicherort-Anzeige
        save_location_frame = ttk.Frame(save_load_frame)
        save_location_frame.pack(fill=tk.X, pady=5)

        ttk.Label(save_location_frame, text="Speicherort:").pack(side=tk.LEFT, padx=5)

        # Speicherort-Pfad
        save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        self.save_location_var = tk.StringVar(value=save_dir)
        save_location_entry = ttk.Entry(save_location_frame, textvariable=self.save_location_var, width=40)
        save_location_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        save_location_entry.config(state="readonly")

        # Über-Bereich
        about_frame = ttk.LabelFrame(settings_frame, text="Über", padding="10")
        about_frame.pack(fill=tk.X, pady=5)

        ttk.Label(about_frame, text="Makro-Recorder", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        ttk.Label(about_frame, text="Version: 1.0").pack(anchor=tk.W)
        ttk.Label(about_frame, text="Ein Programm zur automatischen Erkennung von Objekten auf dem Bildschirm und Ausführung von Tastendrücken.").pack(anchor=tk.W, pady=5)

    def toggle_monitoring(self):
        """Startet oder stoppt die Überwachung"""
        if self.monitoring_active:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """Startet die Überwachung"""
        # Prüfen, ob Objekterkennung-Schritte definiert sind
        has_valid_detection_steps = False

        # Prüfen, ob es gültige Objekterkennung-Schritte gibt
        for step in self.steps:
            if step["active"] and step["screenshot"] and step["region"]:
                has_valid_detection_steps = True
                break

        if not has_valid_detection_steps:
            messagebox.showwarning("Warnung", "Bitte zuerst mindestens einen gültigen Objekterkennung-Schritt definieren.")
            return

        # Überwachung starten
        self.monitoring_active = True
        self.start_stop_button.config(text="Überwachung stoppen")
        self.start_stop_button_tab.config(text="Überwachung stoppen")
        self.status_var.set("Überwachung läuft...")
        self.log("Überwachung gestartet.")

        # Überwachung in einem separaten Thread starten
        self.monitoring_thread = threading.Thread(target=self.monitor_screen)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()

    def stop_monitoring(self):
        """Stoppt die Überwachung"""
        self.monitoring_active = False
        self.start_stop_button.config(text="Überwachung starten")
        self.start_stop_button_tab.config(text="Überwachung starten")
        self.status_var.set("Bereit")
        self.log("Überwachung gestoppt.")

    def monitor_screen(self):
        """Überwacht den Bildschirm"""
        self.log("Starte Bildschirmüberwachung...")

        # Objekterkennung-Schritte überwachen
        self.monitor_steps()

        self.log("Bildschirmüberwachung beendet.")

    def monitor_steps(self):
        """Überwacht die Schritte für die Objekterkennung"""
        if not self.steps:
            return

        self.log("Starte Schrittüberwachung...")

        # Für jeden Schritt einen eigenen Thread starten
        step_threads = []
        for i, step in enumerate(self.steps):
            if step["active"] and step["screenshot"] and step["region"]:
                step_thread = threading.Thread(target=self.monitor_step, args=(i,))
                step_thread.daemon = True
                step_thread.start()
                step_threads.append(step_thread)

        # Warten, bis alle Threads beendet sind
        for thread in step_threads:
            thread.join()

    def monitor_step(self, step_index):
        """Überwacht einen einzelnen Schritt"""
        if step_index < 0 or step_index >= len(self.steps):
            return

        step = self.steps[step_index]

        # Prüfen, ob alle notwendigen Daten vorhanden sind
        if not step["screenshot"] or not step["region"]:
            self.log(f"Fehler: Schritt '{step['name']}' hat keinen Screenshot oder keine Region definiert.")
            return

        x, y, w, h = step["region"]
        key = step["key"]
        confidence = step["confidence"]
        rate = step["rate"]

        self.log(f"Überwache Schritt '{step['name']}': Region ({x}, {y}, {w}, {h}), Taste: {key}, Konfidenz: {confidence:.2f}, Rate: {rate:.2f}")

        # Referenzbild aus dem Screenshot extrahieren
        _, _, screenshot_cv2 = step["screenshot"]
        reference_image = screenshot_cv2[y:y+h, x:x+w]

        # Überwachungsschleife
        last_check_time = 0
        while self.monitoring_active:
            # Prüfen, ob es Zeit für eine neue Überprüfung ist
            current_time = time.time()
            if current_time - last_check_time < rate:
                time.sleep(0.01)  # Kurze Pause, um CPU-Last zu reduzieren
                continue

            last_check_time = current_time

            try:
                # Screenshot machen
                screen = pyautogui.screenshot()
                screen_array = np.array(screen)
                current_screen = cv2.cvtColor(screen_array, cv2.COLOR_RGB2BGR)

                # Region aus dem aktuellen Screenshot extrahieren
                current_region = current_screen[y:y+h, x:x+w]

                # Bilderkennung mit Template Matching
                try:
                    result = cv2.matchTemplate(current_region, reference_image, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, _ = cv2.minMaxLoc(result)

                    # Wenn das Bild erkannt wird, Taste drücken
                    if max_val >= confidence:
                        self.log(f"Schritt '{step['name']}': Bild erkannt (Konfidenz: {max_val:.2f}) - Drücke '{key}'")
                        pyautogui.press(key)

                except cv2.error as e:
                    self.log(f"Fehler bei der Bilderkennung für Schritt '{step['name']}': {str(e)}")
                    continue

            except Exception as e:
                self.log(f"Fehler bei der Bildschirmüberwachung für Schritt '{step['name']}': {str(e)}")
                time.sleep(1)  # Pause bei Fehler

    def add_step(self):
        """Fügt einen neuen Schritt hinzu"""
        # Dialog für Schrittnamen und Taste
        dialog = tk.Toplevel(self.root)
        dialog.title("Neuen Schritt hinzufügen")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Name
        name_frame = ttk.Frame(dialog, padding="10")
        name_frame.pack(fill=tk.X, pady=5)

        ttk.Label(name_frame, text="Name:").pack(anchor=tk.W)
        name_var = tk.StringVar(value=f"Schritt {len(self.steps) + 1}")
        ttk.Entry(name_frame, textvariable=name_var).pack(fill=tk.X, pady=5)

        # Taste
        key_frame = ttk.Frame(dialog, padding="10")
        key_frame.pack(fill=tk.X, pady=5)

        ttk.Label(key_frame, text="Taste:").pack(anchor=tk.W)
        key_var = tk.StringVar(value="space")
        ttk.Entry(key_frame, textvariable=key_var).pack(fill=tk.X, pady=5)

        # Konfidenz
        confidence_frame = ttk.Frame(dialog, padding="10")
        confidence_frame.pack(fill=tk.X, pady=5)

        ttk.Label(confidence_frame, text="Konfidenz:").pack(anchor=tk.W)
        confidence_var = tk.DoubleVar(value=0.7)
        confidence_scale = ttk.Scale(confidence_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=confidence_var)
        confidence_scale.pack(fill=tk.X, pady=5)
        confidence_label = ttk.Label(confidence_frame, text="0.70")
        confidence_label.pack(anchor=tk.E)

        # Aktualisiere das Label, wenn der Schieberegler bewegt wird
        def update_confidence_label(event):
            confidence_label.config(text=f"{confidence_var.get():.2f}")

        confidence_scale.bind("<Motion>", update_confidence_label)

        # Prüfrate
        rate_frame = ttk.Frame(dialog, padding="10")
        rate_frame.pack(fill=tk.X, pady=5)

        ttk.Label(rate_frame, text="Prüfrate (s):").pack(anchor=tk.W)
        rate_var = tk.DoubleVar(value=0.1)
        rate_scale = ttk.Scale(rate_frame, from_=0.01, to=1.0, orient=tk.HORIZONTAL, variable=rate_var)
        rate_scale.pack(fill=tk.X, pady=5)
        rate_label = ttk.Label(rate_frame, text="0.10")
        rate_label.pack(anchor=tk.E)

        # Aktualisiere das Label, wenn der Schieberegler bewegt wird
        def update_rate_label(event):
            rate_label.config(text=f"{rate_var.get():.2f}")

        rate_scale.bind("<Motion>", update_rate_label)

        # Aktiv
        active_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(dialog, text="Aktiv", variable=active_var).pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog, padding="10")
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="Abbrechen", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # Funktion zum Hinzufügen des Schritts
        def add_step_and_close():
            step_name = name_var.get()
            if not step_name:
                step_name = f"Schritt {len(self.steps) + 1}"

            key = key_var.get()
            if not key:
                key = "space"

            confidence = confidence_var.get()
            rate = rate_var.get()
            active = active_var.get()

            # Schritt hinzufügen
            self.steps.append({
                "name": step_name,
                "key": key,
                "confidence": confidence,
                "rate": rate,
                "active": active,
                "screenshot": None,
                "region": None
            })

            # Treeview aktualisieren
            self.update_steps_tree()

            # Dialog schließen
            dialog.destroy()

            # Neuen Schritt auswählen
            self.current_step_id = len(self.steps) - 1
            self.select_step_in_tree(self.current_step_id)

            self.log(f"Schritt '{step_name}' hinzugefügt")

        ttk.Button(button_frame, text="Hinzufügen", command=add_step_and_close).pack(side=tk.RIGHT, padx=5)

    def edit_selected_step(self):
        """Bearbeitet den ausgewählten Schritt"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            messagebox.showwarning("Warnung", "Bitte zuerst einen Schritt auswählen.")
            return

        step = self.steps[self.current_step_id]

        # Dialog für Schrittnamen und Taste
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Schritt bearbeiten: {step['name']}")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Name
        name_frame = ttk.Frame(dialog, padding="10")
        name_frame.pack(fill=tk.X, pady=5)

        ttk.Label(name_frame, text="Name:").pack(anchor=tk.W)
        name_var = tk.StringVar(value=step["name"])
        ttk.Entry(name_frame, textvariable=name_var).pack(fill=tk.X, pady=5)

        # Taste
        key_frame = ttk.Frame(dialog, padding="10")
        key_frame.pack(fill=tk.X, pady=5)

        ttk.Label(key_frame, text="Taste:").pack(anchor=tk.W)
        key_var = tk.StringVar(value=step["key"])
        ttk.Entry(key_frame, textvariable=key_var).pack(fill=tk.X, pady=5)

        # Konfidenz
        confidence_frame = ttk.Frame(dialog, padding="10")
        confidence_frame.pack(fill=tk.X, pady=5)

        ttk.Label(confidence_frame, text="Konfidenz:").pack(anchor=tk.W)
        confidence_var = tk.DoubleVar(value=step["confidence"])
        confidence_scale = ttk.Scale(confidence_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=confidence_var)
        confidence_scale.pack(fill=tk.X, pady=5)
        confidence_label = ttk.Label(confidence_frame, text=f"{step['confidence']:.2f}")
        confidence_label.pack(anchor=tk.E)

        # Aktualisiere das Label, wenn der Schieberegler bewegt wird
        def update_confidence_label(event):
            confidence_label.config(text=f"{confidence_var.get():.2f}")

        confidence_scale.bind("<Motion>", update_confidence_label)

        # Prüfrate
        rate_frame = ttk.Frame(dialog, padding="10")
        rate_frame.pack(fill=tk.X, pady=5)

        ttk.Label(rate_frame, text="Prüfrate (s):").pack(anchor=tk.W)
        rate_var = tk.DoubleVar(value=step["rate"])
        rate_scale = ttk.Scale(rate_frame, from_=0.01, to=1.0, orient=tk.HORIZONTAL, variable=rate_var)
        rate_scale.pack(fill=tk.X, pady=5)
        rate_label = ttk.Label(rate_frame, text=f"{step['rate']:.2f}")
        rate_label.pack(anchor=tk.E)

        # Aktualisiere das Label, wenn der Schieberegler bewegt wird
        def update_rate_label(event):
            rate_label.config(text=f"{rate_var.get():.2f}")

        rate_scale.bind("<Motion>", update_rate_label)

        # Aktiv
        active_var = tk.BooleanVar(value=step["active"])
        ttk.Checkbutton(dialog, text="Aktiv", variable=active_var).pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog, padding="10")
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="Abbrechen", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # Funktion zum Aktualisieren des Schritts
        def update_step_and_close():
            step_name = name_var.get()
            if not step_name:
                step_name = f"Schritt {self.current_step_id + 1}"

            key = key_var.get()
            if not key:
                key = "space"

            confidence = confidence_var.get()
            rate = rate_var.get()
            active = active_var.get()

            # Schritt aktualisieren
            self.steps[self.current_step_id].update({
                "name": step_name,
                "key": key,
                "confidence": confidence,
                "rate": rate,
                "active": active
            })

            # Treeview aktualisieren
            self.update_steps_tree()

            # Dialog schließen
            dialog.destroy()

            # Schritt auswählen
            self.select_step_in_tree(self.current_step_id)

            self.log(f"Schritt '{step_name}' aktualisiert")

        ttk.Button(button_frame, text="Aktualisieren", command=update_step_and_close).pack(side=tk.RIGHT, padx=5)

    def remove_selected_step(self):
        """Entfernt den ausgewählten Schritt"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            messagebox.showwarning("Warnung", "Bitte zuerst einen Schritt auswählen.")
            return

        step_name = self.steps[self.current_step_id]["name"]

        # Bestätigung einholen
        if not messagebox.askyesno("Bestätigung", f"Möchten Sie den Schritt '{step_name}' wirklich entfernen?"):
            return

        # Schritt entfernen
        del self.steps[self.current_step_id]

        # Treeview aktualisieren
        self.update_steps_tree()

        # Aktuellen Schritt zurücksetzen
        self.current_step_id = -1

        self.log(f"Schritt '{step_name}' entfernt")

    def update_steps_tree(self):
        """Aktualisiert den Treeview für die Schritte"""
        # Alle Einträge löschen
        for item in self.steps_tree.get_children():
            self.steps_tree.delete(item)

        # Neue Einträge hinzufügen
        for i, step in enumerate(self.steps):
            region_text = f"({step['region'][0]}, {step['region'][1]}, {step['region'][2]}, {step['region'][3]})" if step["region"] else "Nicht definiert"
            status_text = "Aktiv" if step["active"] else "Inaktiv"

            self.steps_tree.insert("", tk.END, values=(
                step["name"],
                region_text,
                step["key"],
                f"{step['confidence']:.2f}",
                f"{step['rate']:.2f}",
                status_text
            ))

    def select_step_in_tree(self, step_id):
        """Wählt einen Schritt im Treeview aus"""
        if step_id < 0 or step_id >= len(self.steps):
            return

        # Alle Einträge im Treeview durchgehen
        for i, item in enumerate(self.steps_tree.get_children()):
            if i == step_id:
                # Eintrag auswählen
                self.steps_tree.selection_set(item)
                self.steps_tree.focus(item)
                self.steps_tree.see(item)
                break

    def on_step_selected(self, event):
        """Wird aufgerufen, wenn ein Schritt im Treeview ausgewählt wird"""
        selection = self.steps_tree.selection()
        if not selection:
            return

        # Index des ausgewählten Schritts ermitteln
        item = selection[0]
        items = self.steps_tree.get_children()
        self.current_step_id = items.index(item)

        # Schritt-Einstellungen aktualisieren
        step = self.steps[self.current_step_id]
        self.step_key_var.set(step["key"])
        self.step_confidence_scale.set(step["confidence"])
        self.step_confidence_label.config(text=f"{step['confidence']:.2f}")
        self.step_rate_scale.set(step["rate"])
        self.step_rate_label.config(text=f"{step['rate']:.2f}")

        # Screenshot anzeigen, falls vorhanden
        if step["screenshot"]:
            self.show_step_screenshot()

    def update_step_key(self, event):
        """Aktualisiert die Taste für den aktuellen Schritt"""
        key = self.step_key_var.get()
        if not key or self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            return

        # Aktualisiere die Taste des aktuellen Schritts
        self.steps[self.current_step_id]["key"] = key
        self.update_steps_tree()
        self.select_step_in_tree(self.current_step_id)
        self.log(f"Taste für Schritt '{self.steps[self.current_step_id]['name']}' auf '{key}' gesetzt")

    def update_step_confidence(self, value):
        """Aktualisiert die Konfidenz für den aktuellen Schritt"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            return

        # Aktualisiere die Konfidenz des aktuellen Schritts
        confidence = float(value)
        self.steps[self.current_step_id]["confidence"] = confidence
        self.step_confidence_label.config(text=f"{confidence:.2f}")

    def update_step_rate(self, value):
        """Aktualisiert die Prüfrate für den aktuellen Schritt"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            return

        # Aktualisiere die Prüfrate des aktuellen Schritts
        rate = float(value)
        self.steps[self.current_step_id]["rate"] = rate
        self.step_rate_label.config(text=f"{rate:.2f}")

    def take_screenshot_for_step(self):
        """Nimmt einen Screenshot für den aktuellen Schritt auf"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            messagebox.showwarning("Warnung", "Bitte zuerst einen Schritt auswählen.")
            return

        # Benutzer informieren
        messagebox.showinfo("Screenshot aufnehmen", "Das Fenster wird minimiert. Bitte bereiten Sie den Bildschirm vor und drücken Sie dann OK.")

        # Fenster minimieren
        self.root.iconify()

        # Kurze Pause, damit der Benutzer den Bildschirm vorbereiten kann
        time.sleep(1)

        try:
            # Screenshot aufnehmen
            screenshot = pyautogui.screenshot()
            screenshot_array = np.array(screenshot)
            screenshot_cv2 = cv2.cvtColor(screenshot_array, cv2.COLOR_RGB2BGR)

            # Dateinamen generieren
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            step_name = self.steps[self.current_step_id]["name"]
            safe_step_name = "".join(c if c.isalnum() else "_" for c in step_name)
            filename = f"{safe_step_name}_{timestamp}.png"
            filepath = os.path.join("reference_images", filename)

            # Screenshot speichern
            cv2.imwrite(filepath, screenshot_cv2)

            # Screenshot für den Schritt speichern
            self.steps[self.current_step_id]["screenshot"] = (filepath, screenshot, screenshot_cv2)

            # Canvas-Größe anpassen
            self.detection_canvas.config(scrollregion=(0, 0, screenshot.width, screenshot.height))

            # Screenshot anzeigen
            self.photo_image = ImageTk.PhotoImage(screenshot)
            self.detection_canvas.delete("all")
            self.detection_canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            self.detection_screenshot_info_var.set(f"Screenshot für Schritt '{step_name}': {filepath}")

            # Bestehende Schritte anzeigen
            self.show_steps()

            self.log(f"Screenshot für Schritt '{step_name}' aufgenommen: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Aufnehmen des Screenshots: {str(e)}")
        finally:
            # Fenster wiederherstellen
            self.root.deiconify()

    def upload_screenshot_for_step(self):
        """Lädt einen Screenshot für den aktuellen Schritt hoch"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            messagebox.showwarning("Warnung", "Bitte zuerst einen Schritt auswählen.")
            return

        # Datei auswählen
        filepath = filedialog.askopenfilename(
            title="Screenshot auswählen",
            filetypes=[("Bilder", "*.png;*.jpg;*.jpeg;*.bmp")],
            initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "reference_images")
        )

        if not filepath:
            return

        try:
            # Screenshot laden
            screenshot_image = Image.open(filepath)
            screenshot_cv2 = cv2.imread(filepath)

            # Screenshot für den Schritt speichern
            self.steps[self.current_step_id]["screenshot"] = (filepath, screenshot_image, screenshot_cv2)

            # Canvas-Größe anpassen
            self.detection_canvas.config(scrollregion=(0, 0, screenshot_image.width, screenshot_image.height))

            # Screenshot anzeigen
            self.photo_image = ImageTk.PhotoImage(screenshot_image)
            self.detection_canvas.delete("all")
            self.detection_canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            self.detection_screenshot_info_var.set(f"Screenshot für Schritt '{self.steps[self.current_step_id]['name']}': {filepath}")

            # Bestehende Schritte anzeigen
            self.show_steps()

            self.log(f"Screenshot für Schritt '{self.steps[self.current_step_id]['name']}' geladen: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden des Screenshots: {str(e)}")

    def show_step_screenshot(self):
        """Zeigt den Screenshot des aktuellen Schritts an"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            return

        step = self.steps[self.current_step_id]
        if not step["screenshot"]:
            self.detection_canvas.delete("all")
            self.detection_screenshot_info_var.set("Kein Screenshot geladen")
            return

        filepath, screenshot_image, _ = step["screenshot"]

        # Canvas-Größe anpassen
        self.detection_canvas.config(scrollregion=(0, 0, screenshot_image.width, screenshot_image.height))

        # Screenshot anzeigen
        self.photo_image = ImageTk.PhotoImage(screenshot_image)
        self.detection_canvas.delete("all")
        self.detection_canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
        self.detection_screenshot_info_var.set(f"Screenshot für Schritt '{step['name']}': {filepath}")

        # Bestehende Schritte anzeigen
        self.show_steps()

    def show_steps(self):
        """Zeigt die Regionen aller Schritte auf dem Canvas an"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            return

        if not self.steps[self.current_step_id]["screenshot"]:
            return

        # Regionen für alle Schritte anzeigen, die denselben Screenshot verwenden
        for i, step in enumerate(self.steps):
            if step["screenshot"] and step["region"]:
                # Prüfen, ob der Screenshot derselbe ist
                if step["screenshot"][0] == self.steps[self.current_step_id]["screenshot"][0]:
                    x, y, w, h = step["region"]

                    # Farbe basierend auf dem Status
                    if i == self.current_step_id:
                        color = "red"  # Aktiver Schritt
                        width = 3
                    elif step["active"]:
                        color = "orange"  # Aktiver Schritt (nicht ausgewählt)
                        width = 2
                    else:
                        color = "gray"  # Inaktiver Schritt
                        width = 1

                    self.detection_canvas.create_rectangle(x, y, x + w, y + h, outline=color, width=width)
                    self.detection_canvas.create_text(x + 5, y + 5, text=f"Schritt {i+1}: {step['name']}", fill=color, anchor=tk.NW)

    def on_detection_canvas_press(self, event):
        """Wird aufgerufen, wenn die Maustaste auf dem Detection-Canvas gedrückt wird"""
        if self.current_step_id < 0 or self.current_step_id >= len(self.steps):
            messagebox.showwarning("Warnung", "Bitte zuerst einen Schritt auswählen.")
            return

        if not self.steps[self.current_step_id]["screenshot"]:
            messagebox.showwarning("Warnung", "Bitte zuerst einen Screenshot für den Schritt aufnehmen oder laden.")
            return

        # Startpunkt setzen
        self.start_x = self.detection_canvas.canvasx(event.x)
        self.start_y = self.detection_canvas.canvasy(event.y)

        # Rechteck erstellen
        if hasattr(self, 'rect_id') and self.rect_id:
            self.detection_canvas.delete(self.rect_id)

        self.rect_id = self.detection_canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline="red", width=2
        )

        self.selection_active = True

    def on_detection_canvas_drag(self, event):
        """Wird aufgerufen, wenn die Maus bei gedrückter Taste auf dem Detection-Canvas bewegt wird"""
        if not hasattr(self, 'selection_active') or not self.selection_active:
            return

        # Aktuelle Position
        cur_x = self.detection_canvas.canvasx(event.x)
        cur_y = self.detection_canvas.canvasy(event.y)

        # Rechteck aktualisieren
        self.detection_canvas.coords(self.rect_id, self.start_x, self.start_y, cur_x, cur_y)

    def on_detection_canvas_release(self, event):
        """Wird aufgerufen, wenn die Maustaste auf dem Detection-Canvas losgelassen wird"""
        if not hasattr(self, 'selection_active') or not self.selection_active:
            return

        # Endpunkt setzen
        end_x = self.detection_canvas.canvasx(event.x)
        end_y = self.detection_canvas.canvasy(event.y)

        # Rechteck-Koordinaten berechnen
        x1 = min(self.start_x, end_x)
        y1 = min(self.start_y, end_y)
        x2 = max(self.start_x, end_x)
        y2 = max(self.start_y, end_y)

        # Breite und Höhe berechnen
        width = x2 - x1
        height = y2 - y1

        # Prüfen, ob die Auswahl zu klein ist
        if width < 5 or height < 5:
            messagebox.showwarning("Warnung", "Die ausgewählte Region ist zu klein. Bitte wählen Sie eine größere Region aus.")
            self.detection_canvas.delete(self.rect_id)
            self.rect_id = None
            self.selection_active = False
            return

        # Region für den aktuellen Schritt setzen
        self.steps[self.current_step_id]["region"] = (int(x1), int(y1), int(width), int(height))

        # Treeview aktualisieren
        self.update_steps_tree()

        # Schritt auswählen
        self.select_step_in_tree(self.current_step_id)

        self.log(f"Region für Schritt '{self.steps[self.current_step_id]['name']}' definiert: ({int(x1)}, {int(y1)}, {int(width)}, {int(height)})")

        # Auswahl beenden
        self.selection_active = False

        # Schritte anzeigen
        self.show_steps()

    def save_configuration(self):
        """Speichert die Konfiguration in einer Datei"""
        # Datei auswählen
        filepath = filedialog.asksaveasfilename(
            title="Konfiguration speichern",
            filetypes=[("JSON-Dateien", "*.json")],
            defaultextension=".json",
            initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
        )

        if not filepath:
            return

        try:
            # Konfiguration erstellen
            config = {
                "steps": []
            }

            # Schritte speichern
            for step in self.steps:
                # Screenshot-Pfad extrahieren
                screenshot_path = step["screenshot"][0] if step["screenshot"] else None

                # Schritt speichern
                config["steps"].append({
                    "name": step["name"],
                    "key": step["key"],
                    "confidence": step["confidence"],
                    "rate": step["rate"],
                    "active": step["active"],
                    "screenshot_path": screenshot_path,
                    "region": step["region"]
                })

            # Konfiguration speichern
            with open(filepath, "w") as f:
                json.dump(config, f, indent=4)

            self.log(f"Konfiguration gespeichert: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Speichern der Konfiguration: {str(e)}")

    def quick_save(self):
        """Speichert die Konfiguration schnell in einer Datei"""
        # Speicherverzeichnis erstellen, falls es nicht existiert
        save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # Dateinamen generieren
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        filepath = os.path.join(save_dir, f"makro_recorder_{timestamp}.json")

        try:
            # Konfiguration erstellen
            config = {
                "steps": []
            }

            # Schritte speichern
            for step in self.steps:
                # Screenshot-Pfad extrahieren
                screenshot_path = step["screenshot"][0] if step["screenshot"] else None

                # Schritt speichern
                config["steps"].append({
                    "name": step["name"],
                    "key": step["key"],
                    "confidence": step["confidence"],
                    "rate": step["rate"],
                    "active": step["active"],
                    "screenshot_path": screenshot_path,
                    "region": step["region"]
                })

            # Konfiguration speichern
            with open(filepath, "w") as f:
                json.dump(config, f, indent=4)

            self.log(f"Konfiguration schnell gespeichert: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Speichern der Konfiguration: {str(e)}")

    def load_configuration(self):
        """Lädt die Konfiguration aus einer Datei"""
        # Datei auswählen
        filepath = filedialog.askopenfilename(
            title="Konfiguration laden",
            filetypes=[("JSON-Dateien", "*.json")],
            initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
        )

        if not filepath:
            return

        try:
            # Konfiguration laden
            with open(filepath, "r") as f:
                config = json.load(f)

            # Schritte zurücksetzen
            self.steps = []

            # Schritte laden
            if "steps" in config:
                for step_config in config["steps"]:
                    # Schritt erstellen
                    step = {
                        "name": step_config["name"],
                        "key": step_config["key"],
                        "confidence": step_config["confidence"],
                        "rate": step_config["rate"],
                        "active": step_config["active"],
                        "screenshot": None,
                        "region": step_config["region"]
                    }

                    # Screenshot laden, falls vorhanden
                    if "screenshot_path" in step_config and step_config["screenshot_path"]:
                        try:
                            screenshot_path = step_config["screenshot_path"]
                            if os.path.exists(screenshot_path):
                                screenshot_image = Image.open(screenshot_path)
                                screenshot_cv2 = cv2.imread(screenshot_path)
                                step["screenshot"] = (screenshot_path, screenshot_image, screenshot_cv2)
                        except Exception as e:
                            self.log(f"Fehler beim Laden des Screenshots für Schritt '{step['name']}': {str(e)}")

                    # Schritt hinzufügen
                    self.steps.append(step)

            # Treeview aktualisieren
            self.update_steps_tree()

            # Ersten Schritt auswählen, falls vorhanden
            if self.steps:
                self.current_step_id = 0
                self.select_step_in_tree(self.current_step_id)
                self.show_step_screenshot()

            self.log(f"Konfiguration geladen: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden der Konfiguration: {str(e)}")

    def log(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)  # Scrollt zum Ende des Logs
