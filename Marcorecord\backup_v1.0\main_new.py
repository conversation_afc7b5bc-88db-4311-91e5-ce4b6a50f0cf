import tkinter as tk
from tkinter import filedialog
import json
import os
import cv2
import numpy as np
from datetime import datetime
from PIL import Image, ImageTk
from macro_recorder import MacroRecorder
from progress_bar_key_switcher.progress_bar_key_switcher import ProgressBarKeySwitcher

class IntegratedMacroRecorder(MacroRecorder):
    def __init__(self, root):
        """
        Erweitert den Makro-Recorder um den Fortschrittsbalken-Tasten-Wechsler.

        Args:
            root: <PERSON> Tkinter-Root-Fenster.
        """
        # Makro-Recorder initialisieren
        super().__init__(root)

        # Einstellungs-Tab temporär entfernen
        self.notebook.forget(self.settings_tab)

        # Tab für den Fortschrittsbalken-Tasten-Wechsler hinzufügen
        self.progress_bar_tab = tk.Frame(self.notebook)
        self.notebook.add(self.progress_bar_tab, text="Fortschrittsbalken-Wechsler")

        # Einstellungs-Tab wieder hinzufügen (jetzt an letzter Stelle)
        self.notebook.add(self.settings_tab, text="Einstellungen")

        # Fortschrittsbalken-Tasten-Wechsler initialisieren
        self.progress_bar_key_switcher = ProgressBarKeySwitcher(self.progress_bar_tab)

        # Log-Funktion überschreiben, um Logs auch im Makro-Recorder anzuzeigen
        original_log_function = self.progress_bar_key_switcher.log

        def integrated_log(message):
            # Original-Log-Funktion aufrufen
            original_log_function(message)

            # Auch im Makro-Recorder loggen
            self.log(f"[Fortschrittsbalken] {message}")

        # Log-Funktion ersetzen
        self.progress_bar_key_switcher.log = integrated_log

        # Überwachung synchronisieren
        self.original_start_monitoring = self.start_monitoring
        self.original_stop_monitoring = self.stop_monitoring

        # Original-Funktionen des Fortschrittsbalken-Wechslers speichern
        self.original_progress_bar_start_monitoring = self.progress_bar_key_switcher.start_monitoring
        self.original_progress_bar_stop_monitoring = self.progress_bar_key_switcher.stop_monitoring

        # Synchronisierte Start-Funktion für den Makro-Recorder
        def synchronized_start_monitoring():
            # Originale Überwachung starten
            self.original_start_monitoring()

            # Fortschrittsbalken-Überwachung starten, wenn eine Region ausgewählt wurde
            if hasattr(self.progress_bar_key_switcher, 'progress_bar_region') and self.progress_bar_key_switcher.progress_bar_region:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_progress_bar_start_monitoring()
                self.log("Fortschrittsbalken-Überwachung wurde ebenfalls gestartet")

        # Synchronisierte Stopp-Funktion für den Makro-Recorder
        def synchronized_stop_monitoring():
            # Originale Überwachung stoppen
            self.original_stop_monitoring()

            # Fortschrittsbalken-Überwachung stoppen
            if hasattr(self.progress_bar_key_switcher, 'monitoring_active') and self.progress_bar_key_switcher.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_progress_bar_stop_monitoring()
                self.log("Fortschrittsbalken-Überwachung wurde ebenfalls gestoppt")

        # Synchronisierte Start-Funktion für den Fortschrittsbalken-Wechsler
        def synchronized_progress_bar_start_monitoring():
            # Fortschrittsbalken-Überwachung starten
            self.original_progress_bar_start_monitoring()

            # Auch die Objekterkennung starten, wenn Schritte vorhanden sind
            if self.steps and not self.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_start_monitoring()
                self.log("Objekterkennung wurde ebenfalls gestartet")

        # Synchronisierte Stopp-Funktion für den Fortschrittsbalken-Wechsler
        def synchronized_progress_bar_stop_monitoring():
            # Fortschrittsbalken-Überwachung stoppen
            self.original_progress_bar_stop_monitoring()

            # Auch die Objekterkennung stoppen
            if self.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_stop_monitoring()
                self.log("Objekterkennung wurde ebenfalls gestoppt")

        # Überwachungsfunktionen ersetzen
        self.start_monitoring = synchronized_start_monitoring
        self.stop_monitoring = synchronized_stop_monitoring
        self.progress_bar_key_switcher.start_monitoring = synchronized_progress_bar_start_monitoring
        self.progress_bar_key_switcher.stop_monitoring = synchronized_progress_bar_stop_monitoring

        # Speicher- und Ladefunktionen erweitern
        self.original_save_configuration = self.save_configuration
        self.original_quick_save = self.quick_save
        self.original_load_configuration = self.load_configuration

        # Erweiterte Speicherfunktion
        def extended_save_configuration():
            # Originale Speicherfunktion aufrufen
            self.original_save_configuration()

            # Datei auswählen (wir müssen dies erneut tun, da die originale Funktion keinen Pfad zurückgibt)
            filepath = filedialog.asksaveasfilename(
                title="Konfiguration speichern",
                filetypes=[("JSON-Dateien", "*.json")],
                defaultextension=".json",
                initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
            )

            # Wenn keine Datei ausgewählt wurde
            if not filepath:
                return None

            try:
                # Konfiguration laden
                with open(filepath, "r") as f:
                    config = json.load(f)

                # Fortschrittsbalken-Einstellungen hinzufügen
                config["progress_bar_settings"] = {
                    "progress_bar_region": self.progress_bar_key_switcher.progress_bar_region,
                    "progress_threshold": self.progress_bar_key_switcher.progress_threshold,
                    "no_progress_threshold": self.progress_bar_key_switcher.no_progress_threshold,
                    "check_interval": self.progress_bar_key_switcher.check_interval,
                    "key_press_duration": self.progress_bar_key_switcher.key_press_duration,
                    "key_pause_duration": self.progress_bar_key_switcher.key_pause_duration,
                    "bar_visibility_threshold": self.progress_bar_key_switcher.bar_visibility_threshold
                }

                # Screenshot-Pfad speichern, falls vorhanden
                if hasattr(self.progress_bar_key_switcher, 'screenshot') and self.progress_bar_key_switcher.screenshot:
                    # Wenn screenshot ein Tuple ist (filepath, image, cv2_image)
                    if isinstance(self.progress_bar_key_switcher.screenshot, tuple) and len(self.progress_bar_key_switcher.screenshot) > 0:
                        config["progress_bar_settings"]["screenshot_path"] = self.progress_bar_key_switcher.screenshot[0]

                # Konfiguration speichern
                with open(filepath, "w") as f:
                    json.dump(config, f, indent=4)

                self.log("Fortschrittsbalken-Einstellungen wurden ebenfalls gespeichert")
            except Exception as e:
                self.log(f"Fehler beim Speichern der Fortschrittsbalken-Einstellungen: {str(e)}")

            return filepath

        # Speicher- und Ladefunktionen ersetzen
        self.save_configuration = extended_save_configuration

def main():
    """
    Main entry point for the Macro Recorder application.
    Initializes the UI and starts the application.
    """
    # Create the main window
    root = tk.Tk()

    # Initialize the integrated macro recorder
    app = IntegratedMacroRecorder(root)

    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()
