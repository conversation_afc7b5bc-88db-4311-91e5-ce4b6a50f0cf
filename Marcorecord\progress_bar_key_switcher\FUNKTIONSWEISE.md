# Funktionsweise des Fortschrittsbalken-Tasten-Wechslers

## Überblick

Der Fortschrittsbalken-Tasten-Wechsler ist eine Komponente, die automatisch zwischen den Tasten A und D wechselt, um einen Fortschrittsbalken zu füllen. Die Komponente überwacht kontinuierlich den Fortschritt des Balkens und wechselt die Taste, wenn kein Fortschritt mehr erkannt wird.

## Technische Details

### Fortschrittserkennung

1. **Screenshot-Analyse**: Die Komponente nimmt regelmäßig Screenshots des definierten Bereichs auf und analysiert diese.

2. **Bildverarbeitung**:
   - Der Screenshot wird in Graustufen umgewandelt
   - Ein Schwellenwert-Verfahren wird angewendet, um das Bild zu binärisieren (schwarz/weiß)
   - Der Anteil der weißen Pixel wird berechnet, um den Füllstand des Balkens zu bestimmen

3. **Fortschrittsberechnung**:
   - Der Fortschrittswert liegt zwischen 0 und 1 (0 = leer, 1 = voll)
   - Die Differenz zwischen dem letzten und dem aktuellen Wert wird berechnet
   - Wenn die Differenz größer als der Schwellenwert ist, wird ein Fortschritt erkannt

### Tastenwechsel-Logik

1. **Initialisierung**: Die Komponente beginnt mit dem Drücken der Taste A.

2. **Überwachungsschleife**:
   - Der Fortschritt wird regelmäßig überprüft
   - Wenn ein Fortschritt erkannt wird, wird die aktuelle Taste beibehalten
   - Wenn kein Fortschritt erkannt wird, wird ein Zähler erhöht
   - Wenn der Zähler den Schwellenwert erreicht, wird zur anderen Taste gewechselt

3. **Zielerkennung**:
   - Wenn der Fortschrittswert unter einen bestimmten Wert fällt (standardmäßig 5%), wird das Ziel als erreicht betrachtet
   - Die Überwachung wird automatisch beendet

## Parameter

### Fortschritts-Schwellenwert

Dieser Parameter bestimmt, ab welcher Änderung ein Fortschritt erkannt wird. Ein höherer Wert bedeutet, dass größere Änderungen erforderlich sind, um einen Fortschritt zu erkennen.

Standardwert: 0.02 (2%)

### Keine-Fortschritt-Schwellenwert

Dieser Parameter bestimmt, wie viele Prüfungen ohne erkannten Fortschritt durchgeführt werden, bevor die Taste gewechselt wird. Ein höherer Wert bedeutet, dass die Komponente länger wartet, bevor sie die Taste wechselt.

Standardwert: 5 Prüfungen

### Prüfintervall

Dieser Parameter bestimmt, wie oft der Fortschritt überprüft wird. Ein niedrigerer Wert bedeutet häufigere Überprüfungen, was zu schnelleren Reaktionen führt, aber auch mehr CPU-Ressourcen verbraucht.

Standardwert: 0.5 Sekunden

## Anpassung an verschiedene Spiele/Anwendungen

Der Fortschrittsbalken-Tasten-Wechsler kann an verschiedene Spiele und Anwendungen angepasst werden, indem die Parameter entsprechend eingestellt werden:

1. **Schnelle Spiele**: Verwende ein kurzes Prüfintervall (0.1-0.3 Sekunden) und einen niedrigen Keine-Fortschritt-Schwellenwert (2-3).

2. **Langsame Spiele**: Verwende ein längeres Prüfintervall (0.5-1.0 Sekunden) und einen höheren Keine-Fortschritt-Schwellenwert (5-10).

3. **Empfindliche Fortschrittsbalken**: Verwende einen niedrigen Fortschritts-Schwellenwert (0.01-0.02).

4. **Unempfindliche Fortschrittsbalken**: Verwende einen höheren Fortschritts-Schwellenwert (0.05-0.1).
