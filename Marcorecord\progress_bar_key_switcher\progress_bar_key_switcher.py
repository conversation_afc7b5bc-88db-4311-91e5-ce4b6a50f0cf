import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import cv2
import numpy as np
import pyautogui
import time
import os
import threading
import keyboard
from PIL import Image, ImageTk, ImageGrab

class ToolTip:
    """
    Erstellt einen Tooltip für ein Tkinter-Widget.
    """
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        """Zeigt den Tooltip an"""
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        # Tooltip-Fenster erstellen
        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)  # Kein <PERSON>
        self.tooltip_window.wm_geometry(f"+{x}+{y}")

        # Tooltip-Inhalt
        label = ttk.Label(self.tooltip_window, text=self.text, justify=tk.LEFT,
                          background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                          wraplength=300)
        label.pack(padx=5, pady=5)

    def hide_tooltip(self, event=None):
        """Versteckt den Tooltip"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


class ProgressBarKeySwitcher:
    def __init__(self, root=None):
        """
        Initialisiert den Fortschrittsbalken-Tasten-Wechsler.

        Args:
            root: Das Tkinter-Root-Fenster. Wenn None, wird ein neues erstellt.
        """
        self.standalone_mode = root is None

        if self.standalone_mode:
            self.root = tk.Tk()
            self.root.title("Fortschrittsbalken-Tasten-Wechsler")
            self.root.geometry("800x600")
            self.root.resizable(True, True)
        else:
            self.root = root

        # Variablen für die Fortschrittsbalken-Erkennung
        self.progress_bar_region = None  # (x, y, width, height)
        self.monitoring_active = False
        self.monitoring_thread = None
        self.current_key = "a"  # Startet mit Taste A
        self.last_progress_value = 0
        self.no_progress_counter = 0
        self.progress_threshold = 0.01  # Schwellenwert für Fortschrittserkennung (1%)
        self.no_progress_threshold = 3  # Anzahl der Prüfungen ohne Fortschritt, bevor Taste gewechselt wird
        self.check_interval = 0.01  # Intervall in Sekunden zwischen den Prüfungen

        # Neue Variablen für die verbesserte Steuerung
        self.key_press_duration = 2.4  # Dauer des Tastendrucks in Sekunden
        self.key_pause_duration = 0.3  # Pausendauer zwischen Tastendrücken in Sekunden
        self.is_key_pressed = False  # Gibt an, ob gerade eine Taste gedrückt wird
        self.bar_visibility_threshold = 0.01  # Schwellenwert für die Erkennung des Fortschrittsbalkens

        # Variablen für den Einzeltasten-Modus
        self.single_key_mode = False  # Gibt an, ob der Einzeltasten-Modus aktiv ist
        self.single_key = "a"  # Die Taste, die im Einzeltasten-Modus verwendet wird

        # Screenshot für die Region-Auswahl
        self.screenshot = None
        self.photo_image = None

        # Variablen für die Canvas-Interaktion
        self.start_x = 0
        self.start_y = 0
        self.rect_id = None

        # UI erstellen
        self.create_ui()

        if self.standalone_mode:
            self.root.mainloop()

    def create_ui(self):
        """Erstellt die Benutzeroberfläche"""
        # Hauptframe
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Linke Seite: Steuerelemente
        left_frame = ttk.Frame(main_frame, width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # Rechte Seite: Screenshot und Auswahl
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Steuerelemente
        control_frame = ttk.LabelFrame(left_frame, text="Steuerung", padding="10")
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="Screenshot aufnehmen",
                  command=self.take_screenshot).pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="Screenshot laden",
                  command=self.load_screenshot).pack(fill=tk.X, pady=5)

        self.start_stop_button_var = tk.StringVar(value="Überwachung starten")
        self.start_stop_button = ttk.Button(
            control_frame,
            textvariable=self.start_stop_button_var,
            command=self.toggle_monitoring
        )
        self.start_stop_button.pack(fill=tk.X, pady=5)

        # Einstellungen
        settings_frame = ttk.LabelFrame(left_frame, text="Einstellungen", padding="10")
        settings_frame.pack(fill=tk.X, pady=5)

        # Fortschritts-Schwellenwert
        progress_frame = ttk.Frame(settings_frame)
        progress_frame.pack(fill=tk.X, pady=5)

        progress_label = ttk.Label(progress_frame, text="Fortschritts-Schwellenwert:")
        progress_label.pack(anchor=tk.W, padx=5, pady=2)

        # Tooltip für Fortschritts-Schwellenwert
        progress_tooltip = """Bestimmt, wie groß die Änderung im Fortschrittsbalken sein muss, um als Fortschritt zu gelten.

- Niedriger Wert (z.B. 0.01): Erkennt bereits kleine Änderungen als Fortschritt
- Höherer Wert (z.B. 0.05): Erfordert größere Änderungen, um als Fortschritt zu gelten
- Zu niedrig: Kann Bildrauschen fälschlicherweise als Fortschritt erkennen
- Zu hoch: Kann tatsächlichen Fortschritt übersehen

Empfohlen: 0.01-0.03 (1-3%)"""

        # Tooltip-Funktion (wird später definiert)
        self.create_tooltip(progress_label, progress_tooltip)

        # Wert-Anzeige
        value_frame = ttk.Frame(progress_frame)
        value_frame.pack(fill=tk.X, padx=5)

        self.progress_threshold_var = tk.DoubleVar(value=self.progress_threshold)
        self.progress_threshold_label = ttk.Label(value_frame, text=f"{self.progress_threshold:.2f}")
        self.progress_threshold_label.pack(side=tk.RIGHT, padx=5)

        # Slider für Fortschritts-Schwellenwert
        progress_slider = ttk.Scale(
            progress_frame,
            from_=0.01,
            to=0.2,
            orient=tk.HORIZONTAL,
            variable=self.progress_threshold_var,
            command=self.update_progress_threshold_slider,
            length=200
        )
        progress_slider.pack(fill=tk.X, padx=5, pady=5)

        # Keine-Fortschritt-Schwellenwert
        no_progress_frame = ttk.Frame(settings_frame)
        no_progress_frame.pack(fill=tk.X, pady=5)

        no_progress_label = ttk.Label(no_progress_frame, text="Keine-Fortschritt-Schwellenwert:")
        no_progress_label.pack(anchor=tk.W, padx=5, pady=2)

        # Tooltip für Keine-Fortschritt-Schwellenwert
        no_progress_tooltip = """Bestimmt, wie viele Prüfungen ohne Fortschritt durchgeführt werden, bevor zur anderen Taste gewechselt wird.

- Niedriger Wert (z.B. 2-3): Führt zu schnellerem Wechseln zwischen den Tasten
- Höherer Wert (z.B. 5-10): Programm bleibt länger bei einer Taste, bevor es wechselt
- Zu niedrig: Kann zu häufigem, unnötigem Tastenwechsel führen
- Zu hoch: Programm bleibt zu lange bei einer unwirksamen Taste

Empfohlen: 3-5 Prüfungen"""

        # Tooltip-Funktion
        self.create_tooltip(no_progress_label, no_progress_tooltip)

        # Wert-Anzeige
        no_progress_value_frame = ttk.Frame(no_progress_frame)
        no_progress_value_frame.pack(fill=tk.X, padx=5)

        self.no_progress_threshold_var = tk.IntVar(value=self.no_progress_threshold)
        self.no_progress_threshold_label = ttk.Label(no_progress_value_frame, text=f"{self.no_progress_threshold}")
        self.no_progress_threshold_label.pack(side=tk.RIGHT, padx=5)

        # Slider für Keine-Fortschritt-Schwellenwert
        no_progress_slider = ttk.Scale(
            no_progress_frame,
            from_=1,
            to=20,
            orient=tk.HORIZONTAL,
            variable=self.no_progress_threshold_var,
            command=self.update_no_progress_threshold_slider,
            length=200
        )
        no_progress_slider.pack(fill=tk.X, padx=5, pady=5)

        # Prüfintervall
        interval_frame = ttk.Frame(settings_frame)
        interval_frame.pack(fill=tk.X, pady=5)

        interval_label = ttk.Label(interval_frame, text="Prüfintervall (s):")
        interval_label.pack(anchor=tk.W, padx=5, pady=2)

        # Tooltip für Prüfintervall
        interval_tooltip = """Bestimmt, wie oft der Fortschritt überprüft wird (in Sekunden).

- Niedriger Wert (z.B. 0.01-0.05): Häufigere Überprüfungen, schnellere Reaktion
- Höherer Wert (z.B. 0.1-0.5): Seltenere Überprüfungen, geringere CPU-Auslastung
- Zu niedrig: Kann zu hoher CPU-Auslastung führen
- Zu hoch: Kann zu verzögerter Reaktion auf Änderungen führen

Empfohlen: 0.01-0.1 Sekunden für schnelle Reaktion, 0.1-0.5 für ressourcenschonenden Betrieb"""

        # Tooltip-Funktion
        self.create_tooltip(interval_label, interval_tooltip)

        # Wert-Anzeige
        interval_value_frame = ttk.Frame(interval_frame)
        interval_value_frame.pack(fill=tk.X, padx=5)

        self.check_interval_var = tk.DoubleVar(value=self.check_interval)
        self.check_interval_label = ttk.Label(interval_value_frame, text=f"{self.check_interval:.2f}")
        self.check_interval_label.pack(side=tk.RIGHT, padx=5)

        # Slider für Prüfintervall
        interval_slider = ttk.Scale(
            interval_frame,
            from_=0.01,
            to=1.0,
            orient=tk.HORIZONTAL,
            variable=self.check_interval_var,
            command=self.update_check_interval_slider,
            length=200
        )
        interval_slider.pack(fill=tk.X, padx=5, pady=5)

        # Tastendruck-Dauer
        key_press_frame = ttk.Frame(settings_frame)
        key_press_frame.pack(fill=tk.X, pady=5)

        key_press_label = ttk.Label(key_press_frame, text="Tastendruck-Dauer (s):")
        key_press_label.pack(anchor=tk.W, padx=5, pady=2)

        # Tooltip für Tastendruck-Dauer
        key_press_tooltip = """Bestimmt, wie lange eine Taste gedrückt wird, bevor eine Pause eingelegt wird.

- Standardwert: 2.4 Sekunden
- Höhere Werte können in manchen Spielen zu besseren Ergebnissen führen
- Niedrigere Werte können nötig sein, wenn das Spiel kurze Tastendrücke erfordert

Diese Einstellung bestimmt, wie lange die Tasten A oder D gedrückt werden, bevor eine kurze Pause eingelegt wird."""

        # Tooltip-Funktion
        self.create_tooltip(key_press_label, key_press_tooltip)

        # Wert-Anzeige
        key_press_value_frame = ttk.Frame(key_press_frame)
        key_press_value_frame.pack(fill=tk.X, padx=5)

        self.key_press_duration_var = tk.DoubleVar(value=self.key_press_duration)
        self.key_press_duration_label = ttk.Label(key_press_value_frame, text=f"{self.key_press_duration:.1f}")
        self.key_press_duration_label.pack(side=tk.RIGHT, padx=5)

        # Slider für Tastendruck-Dauer
        key_press_slider = ttk.Scale(
            key_press_frame,
            from_=0.1,
            to=10.0,
            orient=tk.HORIZONTAL,
            variable=self.key_press_duration_var,
            command=self.update_key_press_duration_slider,
            length=200
        )
        key_press_slider.pack(fill=tk.X, padx=5, pady=5)

        # Tastenpause-Dauer
        key_pause_frame = ttk.Frame(settings_frame)
        key_pause_frame.pack(fill=tk.X, pady=5)

        key_pause_label = ttk.Label(key_pause_frame, text="Tastenpause-Dauer (s):")
        key_pause_label.pack(anchor=tk.W, padx=5, pady=2)

        # Tooltip für Tastenpause-Dauer
        key_pause_tooltip = """Bestimmt, wie lange zwischen den Tastendrücken pausiert wird.

- Standardwert: 0.3 Sekunden
- Höhere Werte geben dem Spiel mehr Zeit, auf Tastendrücke zu reagieren
- Niedrigere Werte können zu schnelleren Aktionen führen

Diese Pause wird nach jedem Tastendruck-Zyklus eingelegt, bevor die Taste erneut gedrückt wird."""

        # Tooltip-Funktion
        self.create_tooltip(key_pause_label, key_pause_tooltip)

        # Wert-Anzeige
        key_pause_value_frame = ttk.Frame(key_pause_frame)
        key_pause_value_frame.pack(fill=tk.X, padx=5)

        self.key_pause_duration_var = tk.DoubleVar(value=self.key_pause_duration)
        self.key_pause_duration_label = ttk.Label(key_pause_value_frame, text=f"{self.key_pause_duration:.1f}")
        self.key_pause_duration_label.pack(side=tk.RIGHT, padx=5)

        # Slider für Tastenpause-Dauer
        key_pause_slider = ttk.Scale(
            key_pause_frame,
            from_=0.1,
            to=5.0,
            orient=tk.HORIZONTAL,
            variable=self.key_pause_duration_var,
            command=self.update_key_pause_duration_slider,
            length=200
        )
        key_pause_slider.pack(fill=tk.X, padx=5, pady=5)

        # Trennlinie
        ttk.Separator(settings_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # Tastenmodus-Einstellungen
        key_mode_frame = ttk.LabelFrame(settings_frame, text="Tastenmodus", padding="10")
        key_mode_frame.pack(fill=tk.X, pady=5)

        # Checkbox für Einzeltasten-Modus
        self.single_key_mode_var = tk.BooleanVar(value=self.single_key_mode)
        single_key_mode_check = ttk.Checkbutton(
            key_mode_frame,
            text="Einzeltasten-Modus aktivieren",
            variable=self.single_key_mode_var,
            command=self.update_key_mode
        )
        single_key_mode_check.pack(anchor=tk.W, padx=5, pady=5)

        # Tooltip für Einzeltasten-Modus
        single_key_mode_tooltip = """Bestimmt, ob nur eine einzelne Taste gedrückt wird oder zwischen A und D gewechselt wird.

- Aktiviert: Es wird nur die angegebene Taste gedrückt (keine automatische Umschaltung)
- Deaktiviert: Es wird zwischen den Tasten A und D gewechselt, wenn kein Fortschritt erkannt wird

Der Einzeltasten-Modus ist nützlich, wenn Sie wissen, dass nur eine bestimmte Taste benötigt wird."""

        # Tooltip-Funktion
        self.create_tooltip(single_key_mode_check, single_key_mode_tooltip)

        # Eingabefeld für die einzelne Taste
        single_key_frame = ttk.Frame(key_mode_frame)
        single_key_frame.pack(fill=tk.X, pady=5)

        ttk.Label(single_key_frame, text="Einzelne Taste:").pack(side=tk.LEFT, padx=5)

        self.single_key_var = tk.StringVar(value=self.single_key)
        single_key_entry = ttk.Entry(single_key_frame, textvariable=self.single_key_var, width=5)
        single_key_entry.pack(side=tk.LEFT, padx=5)
        single_key_entry.bind("<KeyRelease>", self.update_single_key)

        # Tooltip für Einzeltaste
        single_key_tooltip = """Die Taste, die im Einzeltasten-Modus verwendet wird.

Geben Sie einen einzelnen Buchstaben oder eine Taste ein (z.B. 'a', 'space', 'shift').
Diese Taste wird kontinuierlich gedrückt, wenn der Fortschrittsbalken sichtbar ist."""

        # Tooltip-Funktion
        self.create_tooltip(single_key_entry, single_key_tooltip)

        # Log-Bereich
        log_frame = ttk.LabelFrame(left_frame, text="Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(log_frame, height=10, width=40, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        log_scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        # Screenshot-Bereich
        screenshot_frame = ttk.LabelFrame(right_frame, text="Screenshot", padding="10")
        screenshot_frame.pack(fill=tk.BOTH, expand=True)

        # Canvas für den Screenshot
        canvas_frame = ttk.Frame(screenshot_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.canvas = tk.Canvas(canvas_frame, bg="white")
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Scrollbars für den Canvas
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.canvas.config(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # Canvas-Events
        self.canvas.bind("<ButtonPress-1>", self.on_canvas_press)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)

        # Screenshot-Info
        self.screenshot_info_var = tk.StringVar(value="Kein Screenshot aufgenommen")
        ttk.Label(screenshot_frame, textvariable=self.screenshot_info_var).pack(anchor=tk.W)

        # Initialen Log-Eintrag
        self.log("Fortschrittsbalken-Tasten-Wechsler gestartet")
        self.log("Bitte Screenshot aufnehmen und Fortschrittsbalken-Region auswählen")

    def log(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        print(f"[ProgressBarKeySwitcher] {message}")

        # Debug-Logging in Datei
        try:
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            log_file = os.path.join(log_dir, "progress_bar_debug.log")
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            print(f"[ProgressBarKeySwitcher] Fehler beim Schreiben des Logs in Datei: {str(e)}")

    def take_screenshot(self):
        """Nimmt einen Screenshot auf"""
        self.log("Screenshot wird aufgenommen...")
        self.log(f"DEBUG: Aktuelles Arbeitsverzeichnis: {os.getcwd()}")
        self.log(f"DEBUG: __file__: {__file__}")
        self.log(f"DEBUG: os.path.abspath(__file__): {os.path.abspath(__file__)}")
        self.log(f"DEBUG: os.path.dirname(os.path.abspath(__file__)): {os.path.dirname(os.path.abspath(__file__))}")
        self.log(f"DEBUG: os.path.dirname(os.path.dirname(os.path.abspath(__file__))): {os.path.dirname(os.path.dirname(os.path.abspath(__file__)))}")

        # Fenster minimieren
        self.root.iconify()

        # Kurz warten, damit das Fenster verschwinden kann
        time.sleep(1)

        try:
            # Screenshot aufnehmen
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv2 = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Verzeichnis für Screenshots erstellen, falls es nicht existiert
            # Korrigierter Pfad: Marcorecord/screenshots statt nur screenshots
            screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")
            self.log(f"DEBUG: Screenshots-Verzeichnis-Pfad: {screenshots_dir}")

            if not os.path.exists(screenshots_dir):
                self.log(f"DEBUG: Screenshots-Verzeichnis existiert nicht, versuche es zu erstellen")
                try:
                    os.makedirs(screenshots_dir)
                    self.log(f"Screenshots-Verzeichnis erstellt: {screenshots_dir}")
                except Exception as e:
                    self.log(f"Fehler beim Erstellen des Screenshots-Verzeichnisses: {str(e)}")
                    # Fallback: Versuche, im debug_images-Verzeichnis zu speichern
                    screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
                    self.log(f"DEBUG: Alternatives Verzeichnis-Pfad: {screenshots_dir}")
                    if not os.path.exists(screenshots_dir):
                        try:
                            os.makedirs(screenshots_dir)
                            self.log(f"DEBUG: Alternatives Verzeichnis erstellt")
                        except Exception as e2:
                            self.log(f"DEBUG: Fehler beim Erstellen des alternativen Verzeichnisses: {str(e2)}")
                    self.log(f"Verwende alternatives Verzeichnis: {screenshots_dir}")
            else:
                self.log(f"DEBUG: Screenshots-Verzeichnis existiert bereits")

            # Dateinamen generieren
            timestamp = time.strftime("%Y%m%d-%H%M%S", time.localtime())
            filename = f"progress_bar_{timestamp}.png"
            filepath = os.path.join(screenshots_dir, filename)
            self.log(f"DEBUG: Vollständiger Dateipfad für Screenshot: {filepath}")

            # Screenshot als Datei speichern
            try:
                success = cv2.imwrite(filepath, screenshot_cv2)
                if success:
                    self.log(f"Screenshot gespeichert: {filepath}")
                else:
                    self.log(f"Fehler beim Speichern des Screenshots: cv2.imwrite hat 'False' zurückgegeben")
                    # Versuche, mit PIL zu speichern
                    screenshot.save(filepath)
                    self.log(f"Screenshot mit PIL gespeichert: {filepath}")
            except Exception as e:
                self.log(f"Fehler beim Speichern des Screenshots: {str(e)}")
                # Versuche, im debug_images-Verzeichnis zu speichern
                try:
                    debug_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
                    if not os.path.exists(debug_dir):
                        os.makedirs(debug_dir)
                    debug_filepath = os.path.join(debug_dir, filename)
                    screenshot.save(debug_filepath)
                    self.log(f"Screenshot im Debug-Verzeichnis gespeichert: {debug_filepath}")
                    filepath = debug_filepath
                except Exception as e2:
                    self.log(f"Auch Speichern im Debug-Verzeichnis fehlgeschlagen: {str(e2)}")

            # Screenshot speichern (jetzt mit Dateipfad)
            self.screenshot = (filepath, screenshot, screenshot_np, screenshot_cv2)

            # Canvas-Größe anpassen
            self.canvas.config(scrollregion=(0, 0, screenshot.width, screenshot.height))

            # Screenshot anzeigen
            self.photo_image = ImageTk.PhotoImage(screenshot)
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            self.screenshot_info_var.set(f"Screenshot aufgenommen: {filepath}")

            self.log(f"Screenshot aufgenommen: {screenshot.width}x{screenshot.height}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Aufnehmen des Screenshots: {str(e)}")
        finally:
            # Fenster wiederherstellen
            self.root.deiconify()

    def load_screenshot(self):
        """Lädt einen Screenshot aus einer Datei"""
        self.log("Screenshot wird geladen...")

        # Datei auswählen
        filetypes = [
            ("Alle Bildformate", "*.png;*.jpg;*.jpeg;*.bmp;*.gif"),
            ("PNG", "*.png"),
            ("JPEG", "*.jpg;*.jpeg"),
            ("BMP", "*.bmp"),
            ("GIF", "*.gif")
        ]

        filepath = filedialog.askopenfilename(
            title="Screenshot laden",
            filetypes=filetypes
        )

        if not filepath:
            self.log("Screenshot-Laden abgebrochen")
            return

        try:
            # Bild laden
            screenshot = Image.open(filepath)
            screenshot_np = np.array(screenshot)
            screenshot_cv2 = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

            # Verzeichnis für Screenshots erstellen, falls es nicht existiert
            screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")
            self.log(f"DEBUG: Screenshots-Verzeichnis-Pfad: {screenshots_dir}")

            if not os.path.exists(screenshots_dir):
                self.log(f"DEBUG: Screenshots-Verzeichnis existiert nicht, versuche es zu erstellen")
                try:
                    os.makedirs(screenshots_dir)
                    self.log(f"Screenshots-Verzeichnis erstellt: {screenshots_dir}")
                except Exception as e:
                    self.log(f"Fehler beim Erstellen des Screenshots-Verzeichnisses: {str(e)}")
                    # Fallback: Versuche, im debug_images-Verzeichnis zu speichern
                    screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
                    self.log(f"DEBUG: Alternatives Verzeichnis-Pfad: {screenshots_dir}")
                    if not os.path.exists(screenshots_dir):
                        try:
                            os.makedirs(screenshots_dir)
                            self.log(f"DEBUG: Alternatives Verzeichnis erstellt")
                        except Exception as e2:
                            self.log(f"DEBUG: Fehler beim Erstellen des alternativen Verzeichnisses: {str(e2)}")
                    self.log(f"Verwende alternatives Verzeichnis: {screenshots_dir}")

            # Kopiere den Screenshot in unser Verzeichnis
            timestamp = time.strftime("%Y%m%d-%H%M%S", time.localtime())
            filename = f"progress_bar_loaded_{timestamp}.png"
            new_filepath = os.path.join(screenshots_dir, filename)
            self.log(f"DEBUG: Kopiere Screenshot nach: {new_filepath}")

            try:
                # Kopiere den Screenshot
                screenshot.save(new_filepath)
                self.log(f"Screenshot kopiert: {new_filepath}")

                # Aktualisiere den Dateipfad
                filepath = new_filepath
            except Exception as e:
                self.log(f"Fehler beim Kopieren des Screenshots: {str(e)}")

            # Screenshot speichern (mit Dateipfad an erster Stelle)
            self.screenshot = (filepath, screenshot, screenshot_np, screenshot_cv2)
            self.log(f"DEBUG: Screenshot-Tuple gesetzt: {self.screenshot[0]}")

            # Canvas-Größe anpassen
            self.canvas.config(scrollregion=(0, 0, screenshot.width, screenshot.height))

            # Screenshot anzeigen
            self.photo_image = ImageTk.PhotoImage(screenshot)
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            self.screenshot_info_var.set(f"Screenshot geladen: {filepath}")

            self.log(f"Screenshot geladen: {filepath}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden des Screenshots: {str(e)}")
            self.log(f"Fehler beim Laden des Screenshots: {str(e)}")

    def on_canvas_press(self, event):
        """Wird aufgerufen, wenn die Maustaste auf dem Canvas gedrückt wird"""
        if not self.screenshot:
            messagebox.showwarning("Warnung", "Bitte zuerst einen Screenshot aufnehmen.")
            return

        # Startpunkt setzen
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)

        # Rechteck erstellen
        if hasattr(self, 'rect_id') and self.rect_id:
            self.canvas.delete(self.rect_id)

        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline="red", width=2
        )

    def on_canvas_drag(self, event):
        """Wird aufgerufen, wenn die Maus bei gedrückter Taste bewegt wird"""
        if not self.rect_id:
            return

        # Aktuelle Position
        cur_x = self.canvas.canvasx(event.x)
        cur_y = self.canvas.canvasy(event.y)

        # Rechteck aktualisieren
        self.canvas.coords(self.rect_id, self.start_x, self.start_y, cur_x, cur_y)

    def on_canvas_release(self, event):
        """Wird aufgerufen, wenn die Maustaste losgelassen wird"""
        if not self.rect_id:
            return

        # Endpunkt
        end_x = self.canvas.canvasx(event.x)
        end_y = self.canvas.canvasy(event.y)

        # Region berechnen
        x = min(self.start_x, end_x)
        y = min(self.start_y, end_y)
        width = abs(end_x - self.start_x)
        height = abs(end_y - self.start_y)

        # Region speichern
        self.progress_bar_region = (int(x), int(y), int(width), int(height))

        self.log(f"Fortschrittsbalken-Region ausgewählt: {self.progress_bar_region}")

    def update_progress_threshold_slider(self, value):
        """Aktualisiert den Fortschritts-Schwellenwert über den Slider"""
        try:
            # Wert aus dem Slider holen und auf 2 Dezimalstellen runden
            value = float(value)
            value = round(value, 2)

            # Wert setzen
            self.progress_threshold = value

            # Label aktualisieren
            self.progress_threshold_label.config(text=f"{value:.2f}")

            # Log-Nachricht
            self.log(f"Fortschritts-Schwellenwert auf {value:.2f} gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren des Fortschritts-Schwellenwerts: {str(e)}")

    def update_no_progress_threshold_slider(self, value):
        """Aktualisiert den Keine-Fortschritt-Schwellenwert über den Slider"""
        try:
            # Wert aus dem Slider holen und auf ganze Zahl runden
            value = float(value)
            value = int(value)

            # Wert setzen
            self.no_progress_threshold = value

            # Label aktualisieren
            self.no_progress_threshold_label.config(text=f"{value}")

            # Log-Nachricht
            self.log(f"Keine-Fortschritt-Schwellenwert auf {value} gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren des Keine-Fortschritt-Schwellenwerts: {str(e)}")

    def update_check_interval_slider(self, value):
        """Aktualisiert das Prüfintervall über den Slider"""
        try:
            # Wert aus dem Slider holen und auf 2 Dezimalstellen runden
            value = float(value)
            value = round(value, 2)

            # Wert setzen
            self.check_interval = value

            # Label aktualisieren
            self.check_interval_label.config(text=f"{value:.2f}")

            # Log-Nachricht
            self.log(f"Prüfintervall auf {value:.2f} Sekunden gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren des Prüfintervalls: {str(e)}")

    def update_key_press_duration_slider(self, value):
        """Aktualisiert die Tastendruck-Dauer über den Slider"""
        try:
            # Wert aus dem Slider holen und auf 1 Dezimalstelle runden
            value = float(value)
            value = round(value, 1)

            # Wert setzen
            self.key_press_duration = value

            # Label aktualisieren
            self.key_press_duration_label.config(text=f"{value:.1f}")

            # Log-Nachricht
            self.log(f"Tastendruck-Dauer auf {value:.1f} Sekunden gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren der Tastendruck-Dauer: {str(e)}")

    def update_key_pause_duration_slider(self, value):
        """Aktualisiert die Tastenpause-Dauer über den Slider"""
        try:
            # Wert aus dem Slider holen und auf 1 Dezimalstelle runden
            value = float(value)
            value = round(value, 1)

            # Wert setzen
            self.key_pause_duration = value

            # Label aktualisieren
            self.key_pause_duration_label.config(text=f"{value:.1f}")

            # Log-Nachricht
            self.log(f"Tastenpause-Dauer auf {value:.1f} Sekunden gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren der Tastenpause-Dauer: {str(e)}")

    # Die alten Update-Methoden behalten wir für die Kompatibilität bei
    def update_progress_threshold(self, event=None):
        """Aktualisiert den Fortschritts-Schwellenwert"""
        try:
            value = self.progress_threshold_var.get()
            self.progress_threshold = value
            self.progress_threshold_label.config(text=f"{value:.2f}")
            self.log(f"Fortschritts-Schwellenwert auf {value:.2f} gesetzt")
        except:
            pass

    def update_no_progress_threshold(self, event=None):
        """Aktualisiert den Keine-Fortschritt-Schwellenwert"""
        try:
            value = self.no_progress_threshold_var.get()
            self.no_progress_threshold = value
            self.no_progress_threshold_label.config(text=f"{value}")
            self.log(f"Keine-Fortschritt-Schwellenwert auf {value} gesetzt")
        except:
            pass

    def update_check_interval(self, event=None):
        """Aktualisiert das Prüfintervall"""
        try:
            value = self.check_interval_var.get()
            self.check_interval = value
            self.check_interval_label.config(text=f"{value:.2f}")
            self.log(f"Prüfintervall auf {value:.2f} Sekunden gesetzt")
        except:
            pass

    def update_key_press_duration(self, event=None):
        """Aktualisiert die Tastendruck-Dauer"""
        try:
            value = self.key_press_duration_var.get()
            self.key_press_duration = value
            self.key_press_duration_label.config(text=f"{value:.1f}")
            self.log(f"Tastendruck-Dauer auf {value:.1f} Sekunden gesetzt")
        except:
            pass

    def update_key_pause_duration(self, event=None):
        """Aktualisiert die Tastenpause-Dauer"""
        try:
            value = self.key_pause_duration_var.get()
            self.key_pause_duration = value
            self.key_pause_duration_label.config(text=f"{value:.1f}")
            self.log(f"Tastenpause-Dauer auf {value:.1f} Sekunden gesetzt")
        except:
            pass

    def update_key_mode(self):
        """Aktualisiert den Tastenmodus (Einzeltasten-Modus oder Wechselmodus)"""
        try:
            value = self.single_key_mode_var.get()
            self.single_key_mode = value
            if value:
                self.log(f"Einzeltasten-Modus aktiviert. Taste: '{self.single_key}'")
            else:
                self.log(f"Tastenwechsel-Modus aktiviert (A/D)")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren des Tastenmodus: {str(e)}")

    def update_single_key(self, event=None):
        """Aktualisiert die einzelne Taste für den Einzeltasten-Modus"""
        try:
            value = self.single_key_var.get()
            # Wenn mehr als ein Zeichen eingegeben wurde, nur das erste verwenden
            if len(value) > 1:
                value = value[0]
                self.single_key_var.set(value)

            self.single_key = value
            self.log(f"Einzelne Taste auf '{value}' gesetzt")
        except Exception as e:
            self.log(f"Fehler beim Aktualisieren der einzelnen Taste: {str(e)}")

    def create_tooltip(self, widget, text):
        """Erstellt einen Tooltip für ein Widget"""
        ToolTip(widget, text)

    def toggle_monitoring(self):
        """Startet oder stoppt die Überwachung"""
        if self.monitoring_active:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """Startet die Überwachung"""
        if not self.progress_bar_region:
            messagebox.showwarning("Warnung", "Bitte zuerst die Fortschrittsbalken-Region auswählen.")
            return

        self.monitoring_active = True
        self.start_stop_button_var.set("Überwachung stoppen")

        # Thread starten
        self.monitoring_thread = threading.Thread(target=self.monitor_progress_bar)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()

        self.log("Fortschrittsbalken-Überwachung gestartet")

    def stop_monitoring(self):
        """Stoppt die Überwachung"""
        self.monitoring_active = False
        self.start_stop_button_var.set("Überwachung starten")

        # Alle Tasten loslassen
        keyboard.release('a')
        keyboard.release('d')

        # Auch die einzelne Taste loslassen, falls sie nicht A oder D ist
        if self.single_key_mode and self.single_key not in ['a', 'd']:
            keyboard.release(self.single_key)

        self.log("Fortschrittsbalken-Überwachung gestoppt")

    def monitor_progress_bar(self):
        """Überwacht den Fortschrittsbalken und wechselt die Tasten"""
        self.log("Starte Überwachung des Fortschrittsbalkens...")

        # Initialisierung
        self.last_progress_value = 0
        self.no_progress_counter = 0

        # Taste je nach Modus setzen
        if self.single_key_mode:
            self.current_key = self.single_key
            self.log(f"Einzeltasten-Modus: Verwende Taste '{self.current_key}'")
        else:
            self.current_key = "a"  # Startet mit Taste A
            self.log(f"Tastenwechsel-Modus: Starte mit Taste '{self.current_key}'")

        self.is_key_pressed = False
        last_key_press_time = 0

        # Sicherstellen, dass keine Tasten gedrückt sind
        keyboard.release('a')
        keyboard.release('d')
        if self.single_key_mode and self.single_key not in ['a', 'd']:
            keyboard.release(self.single_key)

        while self.monitoring_active:
            try:
                # Aktuellen Fortschrittswert ermitteln
                current_progress = self.get_progress_value()
                current_time = time.time()

                # Prüfen, ob der Fortschrittsbalken sichtbar ist
                is_bar_visible = self.is_progress_bar_visible()

                # Log-Nachricht
                self.log(f"Fortschritt: {current_progress:.2f}, Balken sichtbar: {is_bar_visible}, Taste: {self.current_key.upper() if self.is_key_pressed else 'Keine'}")

                if not is_bar_visible:
                    # Fortschrittsbalken ist nicht sichtbar, alle Tasten loslassen
                    if self.is_key_pressed:
                        keyboard.release(self.current_key)
                        self.is_key_pressed = False
                        self.log("Fortschrittsbalken nicht sichtbar. Taste losgelassen.")
                else:
                    # Fortschrittsbalken ist sichtbar

                    # Fortschritt berechnen, wenn wir bereits einen vorherigen Wert haben
                    if self.last_progress_value > 0:
                        # Berechne die Differenz - ein negativer Wert bedeutet, dass der Balken nach unten wächst
                        # (mehr farbige Pixel = niedrigerer Fortschrittswert)
                        progress_diff = self.last_progress_value - current_progress

                        # Prüfen, ob Fortschritt gemacht wurde (Balken wächst nach unten)
                        if progress_diff > 0 and abs(progress_diff) >= self.progress_threshold:
                            # Fortschritt wurde gemacht, Counter zurücksetzen
                            self.no_progress_counter = 0
                            self.log(f"Fortschritt erkannt! Differenz: {progress_diff:.4f}")
                        else:
                            # Kein Fortschritt oder Balken geht in die falsche Richtung, Counter erhöhen
                            self.no_progress_counter += 1
                            self.log(f"Kein Fortschritt! Counter: {self.no_progress_counter}/{self.no_progress_threshold}")

                            if self.no_progress_counter >= self.no_progress_threshold:
                                # Im Einzeltasten-Modus nicht wechseln
                                if not self.single_key_mode:
                                    # Taste wechseln
                                    if self.is_key_pressed:
                                        keyboard.release(self.current_key)
                                        self.is_key_pressed = False
                                        self.log(f"Taste '{self.current_key.upper()}' losgelassen")

                                    # Zur anderen Taste wechseln
                                    self.current_key = "d" if self.current_key == "a" else "a"
                                    self.log(f"Kein Fortschritt erkannt! Wechsle zu Taste '{self.current_key.upper()}'")
                                else:
                                    # Im Einzeltasten-Modus nur loggen
                                    self.log(f"Kein Fortschritt erkannt! Bleibe bei Taste '{self.single_key}' (Einzeltasten-Modus)")

                                # Counter zurücksetzen
                                self.no_progress_counter = 0

                    # Aktuellen Fortschrittswert speichern
                    self.last_progress_value = current_progress

                    # Tasten-Timing-Logik
                    if not self.is_key_pressed and (current_time - last_key_press_time) >= self.key_pause_duration:
                        # Taste drücken
                        keyboard.press(self.current_key)
                        self.is_key_pressed = True
                        press_start_time = current_time
                        self.log(f"Taste '{self.current_key.upper()}' gedrückt")

                        # Taste für die festgelegte Dauer gedrückt halten, aber währenddessen Fortschritt prüfen
                        key_press_end_time = press_start_time + self.key_press_duration
                        check_interval = min(0.1, self.check_interval)  # Maximal alle 0,1 Sekunden prüfen

                        while time.time() < key_press_end_time and self.monitoring_active:
                            # Aktuellen Fortschrittswert ermitteln
                            current_progress = self.get_progress_value()
                            is_bar_visible = self.is_progress_bar_visible()

                            # Wenn der Balken nicht mehr sichtbar ist, Taste loslassen und Schleife beenden
                            if not is_bar_visible:
                                keyboard.release(self.current_key)
                                self.is_key_pressed = False
                                self.log(f"Taste '{self.current_key.upper()}' losgelassen (Balken nicht mehr sichtbar)")
                                break

                            # Fortschritt berechnen
                            if self.last_progress_value > 0:
                                progress_diff = self.last_progress_value - current_progress

                                # Prüfen, ob Fortschritt gemacht wurde
                                if progress_diff > 0 and abs(progress_diff) >= self.progress_threshold:
                                    # Fortschritt wurde gemacht, Counter zurücksetzen
                                    self.no_progress_counter = 0
                                    self.log(f"Fortschritt erkannt! Differenz: {progress_diff:.4f}")
                                else:
                                    # Kein Fortschritt, Counter erhöhen
                                    self.no_progress_counter += 1
                                    self.log(f"Kein Fortschritt! Counter: {self.no_progress_counter}/{self.no_progress_threshold}")

                                    # Wenn der Counter den Schwellenwert erreicht hat, Taste wechseln
                                    if self.no_progress_counter >= self.no_progress_threshold:
                                        # Im Einzeltasten-Modus nicht wechseln
                                        if not self.single_key_mode:
                                            # Aktuelle Taste loslassen
                                            keyboard.release(self.current_key)
                                            self.is_key_pressed = False
                                            self.log(f"Taste '{self.current_key.upper()}' losgelassen")

                                            # Zur anderen Taste wechseln
                                            self.current_key = "d" if self.current_key == "a" else "a"
                                            self.log(f"Kein Fortschritt erkannt! Wechsle zu Taste '{self.current_key.upper()}'")

                                            # Neue Taste drücken
                                            keyboard.press(self.current_key)
                                            self.is_key_pressed = True
                                            self.log(f"Taste '{self.current_key.upper()}' gedrückt")
                                        else:
                                            # Im Einzeltasten-Modus nur loggen
                                            self.log(f"Kein Fortschritt erkannt! Bleibe bei Taste '{self.single_key}' (Einzeltasten-Modus)")

                                        # Counter zurücksetzen
                                        self.no_progress_counter = 0

                            # Aktuellen Fortschrittswert speichern
                            self.last_progress_value = current_progress

                            # Kurz warten
                            time.sleep(check_interval)

                        # Taste loslassen, wenn sie noch gedrückt ist
                        if self.is_key_pressed:
                            keyboard.release(self.current_key)
                            self.is_key_pressed = False
                            self.log(f"Taste '{self.current_key.upper()}' losgelassen")

                        # Zeit aktualisieren
                        last_key_press_time = time.time()

                # Prüfen, ob das Ziel erreicht wurde (Fortschrittsbalken ganz unten)
                if current_progress <= 0.05 and is_bar_visible:  # 5% oder weniger und Balken ist sichtbar
                    self.log("Ziel erreicht! Fortschrittsbalken ist fast leer.")
                    self.stop_monitoring()
                    break

                # Warten
                time.sleep(self.check_interval)

            except Exception as e:
                self.log(f"Fehler bei der Fortschrittsbalken-Überwachung: {str(e)}")
                time.sleep(1)

        # Alle Tasten loslassen
        keyboard.release('a')
        keyboard.release('d')

        self.log("Fortschrittsbalken-Überwachung beendet")

    def get_progress_value(self):
        """
        Ermittelt den aktuellen Fortschrittswert des Balkens.

        Returns:
            float: Fortschrittswert zwischen 0 und 1, wobei 0 = leer (Ziel erreicht) und 1 = voll
        """
        if not self.progress_bar_region:
            return 1.0

        try:
            # Screenshot der Region machen
            x, y, width, height = self.progress_bar_region
            region_screenshot = pyautogui.screenshot(region=(x, y, width, height))
            region_np = np.array(region_screenshot)

            # Debug-Verzeichnis erstellen, falls es nicht existiert
            debug_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # Debug-Bild speichern
            debug_path = os.path.join(debug_dir, "debug_progress.png")
            cv2.imwrite(debug_path, cv2.cvtColor(region_np, cv2.COLOR_RGB2BGR))

            # In HSV-Farbraum umwandeln für bessere Farberkennung
            hsv = cv2.cvtColor(region_np, cv2.COLOR_RGB2HSV)

            # Erweiterte Farbbereiche für Orange/Gelb/Braun definieren
            # Erster Bereich: Orange/Gelb
            lower_yellow = np.array([15, 50, 50])
            upper_yellow = np.array([45, 255, 255])
            yellow_mask = cv2.inRange(hsv, lower_yellow, upper_yellow)

            # Zweiter Bereich: Braun/Dunkelorange
            lower_orange = np.array([5, 50, 50])
            upper_orange = np.array([20, 255, 255])
            orange_mask = cv2.inRange(hsv, lower_orange, upper_orange)

            # Masken kombinieren
            combined_mask = cv2.bitwise_or(yellow_mask, orange_mask)

            # Gefüllten Bereich berechnen (farbige Pixel)
            colored_pixels = np.sum(combined_mask > 0)
            total_pixels = combined_mask.size

            # Fortschrittswert berechnen (0 = leer, 1 = voll)
            # Wir invertieren den Wert, da der Balken von oben nach unten gefüllt wird
            progress_value = 1.0 - (colored_pixels / total_pixels)

            # Debug-Ausgabe
            self.log(f"Fortschrittswert: {progress_value:.4f} (Farbpixel: {colored_pixels}, Gesamt: {total_pixels})")

            return progress_value

        except Exception as e:
            self.log(f"Fehler bei der Fortschrittswert-Ermittlung: {str(e)}")
            return 1.0

    def is_progress_bar_visible(self):
        """
        Prüft, ob der Fortschrittsbalken sichtbar ist.

        Der Fortschrittsbalken ist orange/gelb, daher suchen wir nach diesen Farben.

        Returns:
            bool: True, wenn der Fortschrittsbalken sichtbar ist, sonst False
        """
        if not self.progress_bar_region:
            return False

        try:
            # Screenshot der Region machen
            x, y, width, height = self.progress_bar_region
            region_screenshot = pyautogui.screenshot(region=(x, y, width, height))
            region_np = np.array(region_screenshot)

            # Debug-Verzeichnis erstellen, falls es nicht existiert
            debug_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # Debug-Bild speichern
            debug_path = os.path.join(debug_dir, "debug_progress_orig.png")
            cv2.imwrite(debug_path, cv2.cvtColor(region_np, cv2.COLOR_RGB2BGR))

            # In HSV-Farbraum umwandeln für bessere Farberkennung
            hsv = cv2.cvtColor(region_np, cv2.COLOR_RGB2HSV)

            # Erweiterte Farbbereiche für Orange/Gelb/Braun definieren
            # Erster Bereich: Orange/Gelb
            lower_yellow = np.array([15, 50, 50])
            upper_yellow = np.array([45, 255, 255])
            yellow_mask = cv2.inRange(hsv, lower_yellow, upper_yellow)

            # Zweiter Bereich: Braun/Dunkelorange
            lower_orange = np.array([5, 50, 50])
            upper_orange = np.array([20, 255, 255])
            orange_mask = cv2.inRange(hsv, lower_orange, upper_orange)

            # Masken kombinieren
            combined_mask = cv2.bitwise_or(yellow_mask, orange_mask)

            # Debug-Verzeichnis erstellen, falls es nicht existiert
            debug_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "debug_images")
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # Debug-Masken speichern
            cv2.imwrite(os.path.join(debug_dir, "debug_yellow_mask.png"), yellow_mask)
            cv2.imwrite(os.path.join(debug_dir, "debug_orange_mask.png"), orange_mask)
            cv2.imwrite(os.path.join(debug_dir, "debug_combined_mask.png"), combined_mask)

            # Anzahl der Pixel im Farbbereich
            colored_pixels = np.sum(combined_mask > 0)
            total_pixels = combined_mask.size

            # Prozentsatz der farbigen Pixel berechnen
            color_percentage = colored_pixels / total_pixels

            # Debug-Ausgabe
            self.log(f"Farbanteil: {color_percentage:.4f}")

            # Wenn mehr als der Schwellenwert der Pixel im Farbbereich liegen, ist der Balken sichtbar
            return color_percentage > self.bar_visibility_threshold

        except Exception as e:
            self.log(f"Fehler bei der Balken-Sichtbarkeitsprüfung: {str(e)}")
            return False

# Standalone-Modus
if __name__ == "__main__":
    app = ProgressBarKeySwitcher()
