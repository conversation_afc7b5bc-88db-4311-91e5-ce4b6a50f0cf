import tkinter as tk
from tkinter import filedialog, ttk
import json
import os
import cv2
import numpy as np
from datetime import datetime
from PIL import Image, ImageTk
from macro_recorder import MacroRecorder
from progress_bar_key_switcher.progress_bar_key_switcher import ProgressBarKeySwitcher

class IntegratedMacroRecorder(MacroRecorder):
    def __init__(self, root):
        """
        Erweitert den Makro-Recorder um den Fortschrittsbalken-Tasten-Wechsler.

        Args:
            root: Das Tkinter-Root-Fenster.
        """
        # Makro-Recorder initialisieren
        super().__init__(root)

        # Einstellungs-Tab temporär entfernen
        self.notebook.forget(self.settings_tab)

        # Tab für den Fortschrittsbalken-Tasten-Wechsler hinzufügen
        self.progress_bar_tab = tk.Frame(self.notebook)
        self.notebook.add(self.progress_bar_tab, text="Fortschrittsbalken-Wechsler")

        # Einstellungs-Tab wieder hinzufügen (jetzt an letzter Stelle)
        self.notebook.add(self.settings_tab, text="Einstellungen")

        # Fortschrittsbalken-Tasten-Wechsler initialisieren
        self.progress_bar_key_switcher = ProgressBarKeySwitcher(self.progress_bar_tab)

        # Log-Funktion überschreiben, um Logs auch im Makro-Recorder anzuzeigen
        original_log_function = self.progress_bar_key_switcher.log

        def integrated_log(message):
            # Original-Log-Funktion aufrufen
            original_log_function(message)

            # Auch im Makro-Recorder loggen
            self.log(f"[Fortschrittsbalken] {message}")

        # Log-Funktion ersetzen
        self.progress_bar_key_switcher.log = integrated_log

        # Überwachung synchronisieren
        self.original_start_monitoring = self.start_monitoring
        self.original_stop_monitoring = self.stop_monitoring

        # Original-Funktionen des Fortschrittsbalken-Wechslers speichern
        self.original_progress_bar_start_monitoring = self.progress_bar_key_switcher.start_monitoring
        self.original_progress_bar_stop_monitoring = self.progress_bar_key_switcher.stop_monitoring

        # Synchronisierte Start-Funktion für den Makro-Recorder
        def synchronized_start_monitoring():
            # Originale Überwachung starten
            self.original_start_monitoring()

            # Fortschrittsbalken-Überwachung starten, wenn eine Region ausgewählt wurde
            if hasattr(self.progress_bar_key_switcher, 'progress_bar_region') and self.progress_bar_key_switcher.progress_bar_region:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_progress_bar_start_monitoring()
                self.log("Fortschrittsbalken-Überwachung wurde ebenfalls gestartet")

        # Synchronisierte Stopp-Funktion für den Makro-Recorder
        def synchronized_stop_monitoring():
            # Originale Überwachung stoppen
            self.original_stop_monitoring()

            # Fortschrittsbalken-Überwachung stoppen
            if hasattr(self.progress_bar_key_switcher, 'monitoring_active') and self.progress_bar_key_switcher.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_progress_bar_stop_monitoring()
                self.log("Fortschrittsbalken-Überwachung wurde ebenfalls gestoppt")

        # Synchronisierte Start-Funktion für den Fortschrittsbalken-Wechsler
        def synchronized_progress_bar_start_monitoring():
            # Fortschrittsbalken-Überwachung starten
            self.original_progress_bar_start_monitoring()

            # Auch die Objekterkennung starten, wenn Schritte vorhanden sind
            if self.steps and not self.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_start_monitoring()
                self.log("Objekterkennung wurde ebenfalls gestartet")

        # Synchronisierte Stopp-Funktion für den Fortschrittsbalken-Wechsler
        def synchronized_progress_bar_stop_monitoring():
            # Fortschrittsbalken-Überwachung stoppen
            self.original_progress_bar_stop_monitoring()

            # Auch die Objekterkennung stoppen
            if self.monitoring_active:
                # Direkt die Original-Funktion aufrufen, um Rekursion zu vermeiden
                self.original_stop_monitoring()
                self.log("Objekterkennung wurde ebenfalls gestoppt")

        # Überwachungsfunktionen ersetzen
        self.start_monitoring = synchronized_start_monitoring
        self.stop_monitoring = synchronized_stop_monitoring
        self.progress_bar_key_switcher.start_monitoring = synchronized_progress_bar_start_monitoring
        self.progress_bar_key_switcher.stop_monitoring = synchronized_progress_bar_stop_monitoring

        # Speicher- und Ladefunktionen erweitern
        self.original_save_configuration = self.save_configuration
        self.original_quick_save = self.quick_save
        self.original_load_configuration = self.load_configuration

        # Erweiterte Speicherfunktion
        def extended_save_configuration():
            # Datei auswählen
            filepath = filedialog.asksaveasfilename(
                title="Konfiguration speichern",
                filetypes=[("JSON-Dateien", "*.json")],
                defaultextension=".json",
                initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
            )

            # Wenn keine Datei ausgewählt wurde
            if not filepath:
                return None

            try:
                # Konfiguration erstellen
                config = {
                    "steps": []
                }

                # Schritte speichern
                for step in self.steps:
                    # Screenshot-Pfad extrahieren
                    screenshot_path = step["screenshot"][0] if step["screenshot"] else None

                    # Schritt speichern
                    config["steps"].append({
                        "name": step["name"],
                        "key": step["key"],
                        "confidence": step["confidence"],
                        "rate": step["rate"],
                        "active": step["active"],
                        "screenshot_path": screenshot_path,
                        "region": step["region"]
                    })

                # Fortschrittsbalken-Einstellungen hinzufügen
                config["progress_bar_settings"] = {
                    "progress_bar_region": self.progress_bar_key_switcher.progress_bar_region,
                    "progress_threshold": self.progress_bar_key_switcher.progress_threshold,
                    "no_progress_threshold": self.progress_bar_key_switcher.no_progress_threshold,
                    "check_interval": self.progress_bar_key_switcher.check_interval,
                    "key_press_duration": self.progress_bar_key_switcher.key_press_duration,
                    "key_pause_duration": self.progress_bar_key_switcher.key_pause_duration,
                    "bar_visibility_threshold": self.progress_bar_key_switcher.bar_visibility_threshold,
                    "single_key_mode": self.progress_bar_key_switcher.single_key_mode,
                    "single_key": self.progress_bar_key_switcher.single_key
                }

                # Screenshot-Pfad speichern, falls vorhanden
                self.log(f"DEBUG: Prüfe Screenshot-Attribut: hasattr={hasattr(self.progress_bar_key_switcher, 'screenshot')}")
                if hasattr(self.progress_bar_key_switcher, 'screenshot'):
                    self.log(f"DEBUG: Screenshot-Wert: {self.progress_bar_key_switcher.screenshot}")

                if hasattr(self.progress_bar_key_switcher, 'screenshot') and self.progress_bar_key_switcher.screenshot:
                    # Wenn screenshot ein Tuple ist (filepath, image, screenshot_np, screenshot_cv2)
                    self.log(f"DEBUG: Screenshot ist vom Typ: {type(self.progress_bar_key_switcher.screenshot)}")
                    self.log(f"DEBUG: Screenshot hat Länge: {len(self.progress_bar_key_switcher.screenshot) if isinstance(self.progress_bar_key_switcher.screenshot, tuple) else 'nicht anwendbar'}")

                    if isinstance(self.progress_bar_key_switcher.screenshot, tuple) and len(self.progress_bar_key_switcher.screenshot) > 0:
                        self.log(f"DEBUG: Screenshot[0] ist: {self.progress_bar_key_switcher.screenshot[0]}")
                        config["progress_bar_settings"]["screenshot_path"] = self.progress_bar_key_switcher.screenshot[0]
                        self.log(f"Fortschrittsbalken-Screenshot gespeichert: {self.progress_bar_key_switcher.screenshot[0]}")
                    else:
                        self.log(f"DEBUG: Screenshot ist kein Tuple oder leer")

                # Konfiguration speichern
                with open(filepath, "w") as f:
                    json.dump(config, f, indent=4)

                self.log(f"Konfiguration gespeichert: {filepath}")
                self.log("Fortschrittsbalken-Einstellungen wurden ebenfalls gespeichert")
            except Exception as e:
                self.log(f"Fehler beim Speichern: {str(e)}")
                import traceback
                self.log(f"Details: {traceback.format_exc()}")

            return filepath

        # Erweiterte Schnellspeicherfunktion
        def extended_quick_save():
            # Speicherverzeichnis erstellen, falls es nicht existiert
            save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # Dateinamen generieren
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filepath = os.path.join(save_dir, f"makro_recorder_{timestamp}.json")

            try:
                # Konfiguration erstellen
                config = {
                    "steps": []
                }

                # Schritte speichern
                for step in self.steps:
                    # Screenshot-Pfad extrahieren
                    screenshot_path = step["screenshot"][0] if step["screenshot"] else None

                    # Schritt speichern
                    config["steps"].append({
                        "name": step["name"],
                        "key": step["key"],
                        "confidence": step["confidence"],
                        "rate": step["rate"],
                        "active": step["active"],
                        "screenshot_path": screenshot_path,
                        "region": step["region"]
                    })

                # Fortschrittsbalken-Einstellungen hinzufügen
                config["progress_bar_settings"] = {
                    "progress_bar_region": self.progress_bar_key_switcher.progress_bar_region,
                    "progress_threshold": self.progress_bar_key_switcher.progress_threshold,
                    "no_progress_threshold": self.progress_bar_key_switcher.no_progress_threshold,
                    "check_interval": self.progress_bar_key_switcher.check_interval,
                    "key_press_duration": self.progress_bar_key_switcher.key_press_duration,
                    "key_pause_duration": self.progress_bar_key_switcher.key_pause_duration,
                    "bar_visibility_threshold": self.progress_bar_key_switcher.bar_visibility_threshold,
                    "single_key_mode": self.progress_bar_key_switcher.single_key_mode,
                    "single_key": self.progress_bar_key_switcher.single_key
                }

                # Screenshot-Pfad speichern, falls vorhanden
                self.log(f"DEBUG: Prüfe Screenshot-Attribut: hasattr={hasattr(self.progress_bar_key_switcher, 'screenshot')}")
                if hasattr(self.progress_bar_key_switcher, 'screenshot'):
                    self.log(f"DEBUG: Screenshot-Wert: {self.progress_bar_key_switcher.screenshot}")

                if hasattr(self.progress_bar_key_switcher, 'screenshot') and self.progress_bar_key_switcher.screenshot:
                    # Wenn screenshot ein Tuple ist (filepath, image, screenshot_np, screenshot_cv2)
                    self.log(f"DEBUG: Screenshot ist vom Typ: {type(self.progress_bar_key_switcher.screenshot)}")
                    self.log(f"DEBUG: Screenshot hat Länge: {len(self.progress_bar_key_switcher.screenshot) if isinstance(self.progress_bar_key_switcher.screenshot, tuple) else 'nicht anwendbar'}")

                    if isinstance(self.progress_bar_key_switcher.screenshot, tuple) and len(self.progress_bar_key_switcher.screenshot) > 0:
                        self.log(f"DEBUG: Screenshot[0] ist: {self.progress_bar_key_switcher.screenshot[0]}")
                        config["progress_bar_settings"]["screenshot_path"] = self.progress_bar_key_switcher.screenshot[0]
                        self.log(f"Fortschrittsbalken-Screenshot gespeichert: {self.progress_bar_key_switcher.screenshot[0]}")
                    else:
                        self.log(f"DEBUG: Screenshot ist kein Tuple oder leer")

                # Konfiguration speichern
                with open(filepath, "w") as f:
                    json.dump(config, f, indent=4)

                self.log(f"Konfiguration schnell gespeichert: {filepath}")
                self.log("Fortschrittsbalken-Einstellungen wurden ebenfalls gespeichert")
            except Exception as e:
                self.log(f"Fehler beim Schnellspeichern: {str(e)}")
                import traceback
                self.log(f"Details: {traceback.format_exc()}")

            return filepath

        # Erweiterte Ladefunktion
        def extended_load_configuration():
            # Datei auswählen
            filepath = filedialog.askopenfilename(
                title="Konfiguration laden",
                filetypes=[("JSON-Dateien", "*.json")],
                initialdir=os.path.join(os.path.dirname(os.path.abspath(__file__)), "saves")
            )

            # Wenn keine Datei ausgewählt wurde
            if not filepath:
                return None

            try:
                # Konfiguration laden
                with open(filepath, "r") as f:
                    config = json.load(f)

                # Schritte zurücksetzen
                self.steps = []

                # Schritte laden
                if "steps" in config:
                    for step_config in config["steps"]:
                        # Schritt erstellen
                        step = {
                            "name": step_config["name"],
                            "key": step_config["key"],
                            "confidence": step_config["confidence"],
                            "rate": step_config["rate"],
                            "active": step_config["active"],
                            "screenshot": None,
                            "region": step_config["region"]
                        }

                        # Screenshot laden, falls vorhanden
                        if "screenshot_path" in step_config and step_config["screenshot_path"]:
                            try:
                                screenshot_path = step_config["screenshot_path"]
                                if os.path.exists(screenshot_path):
                                    screenshot_image = Image.open(screenshot_path)
                                    screenshot_cv2 = cv2.imread(screenshot_path)
                                    step["screenshot"] = (screenshot_path, screenshot_image, screenshot_cv2)
                            except Exception as e:
                                self.log(f"Fehler beim Laden des Screenshots für Schritt '{step['name']}': {str(e)}")

                        # Schritt hinzufügen
                        self.steps.append(step)

                # Treeview aktualisieren
                self.update_steps_tree()

                # Ersten Schritt auswählen, falls vorhanden
                if self.steps:
                    self.current_step_id = 0
                    self.select_step_in_tree(self.current_step_id)
                    self.show_step_screenshot()

                # Fortschrittsbalken-Einstellungen laden, falls vorhanden
                if "progress_bar_settings" in config:
                    settings = config["progress_bar_settings"]

                    # Region setzen
                    if "progress_bar_region" in settings and settings["progress_bar_region"]:
                        self.progress_bar_key_switcher.progress_bar_region = settings["progress_bar_region"]

                    # Schwellenwerte setzen
                    if "progress_threshold" in settings:
                        self.progress_bar_key_switcher.progress_threshold = settings["progress_threshold"]
                        self.progress_bar_key_switcher.progress_threshold_var.set(settings["progress_threshold"])
                        self.progress_bar_key_switcher.progress_threshold_label.config(text=f"{settings['progress_threshold']:.2f}")

                    if "no_progress_threshold" in settings:
                        self.progress_bar_key_switcher.no_progress_threshold = settings["no_progress_threshold"]
                        self.progress_bar_key_switcher.no_progress_threshold_var.set(settings["no_progress_threshold"])
                        self.progress_bar_key_switcher.no_progress_threshold_label.config(text=f"{settings['no_progress_threshold']}")

                    if "check_interval" in settings:
                        self.progress_bar_key_switcher.check_interval = settings["check_interval"]
                        self.progress_bar_key_switcher.check_interval_var.set(settings["check_interval"])
                        self.progress_bar_key_switcher.check_interval_label.config(text=f"{settings['check_interval']:.2f}")

                    if "key_press_duration" in settings:
                        self.progress_bar_key_switcher.key_press_duration = settings["key_press_duration"]
                        self.progress_bar_key_switcher.key_press_duration_var.set(settings["key_press_duration"])
                        self.progress_bar_key_switcher.key_press_duration_label.config(text=f"{settings['key_press_duration']:.1f}")

                    if "key_pause_duration" in settings:
                        self.progress_bar_key_switcher.key_pause_duration = settings["key_pause_duration"]
                        self.progress_bar_key_switcher.key_pause_duration_var.set(settings["key_pause_duration"])
                        self.progress_bar_key_switcher.key_pause_duration_label.config(text=f"{settings['key_pause_duration']:.1f}")

                    if "bar_visibility_threshold" in settings:
                        self.progress_bar_key_switcher.bar_visibility_threshold = settings["bar_visibility_threshold"]

                    # Einzeltasten-Modus laden
                    if "single_key_mode" in settings:
                        self.progress_bar_key_switcher.single_key_mode = settings["single_key_mode"]
                        self.progress_bar_key_switcher.single_key_mode_var.set(settings["single_key_mode"])

                    # Einzelne Taste laden
                    if "single_key" in settings:
                        self.progress_bar_key_switcher.single_key = settings["single_key"]
                        self.progress_bar_key_switcher.single_key_var.set(settings["single_key"])

                    # Screenshot laden, falls vorhanden
                    if "screenshot_path" in settings and settings["screenshot_path"]:
                        try:
                            screenshot_path = settings["screenshot_path"]
                            if os.path.exists(screenshot_path):
                                # Screenshot laden
                                screenshot = Image.open(screenshot_path)
                                screenshot_np = np.array(screenshot)
                                screenshot_cv2 = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                                # Screenshot speichern
                                self.progress_bar_key_switcher.screenshot = (screenshot_path, screenshot, screenshot_np, screenshot_cv2)

                                # Canvas-Größe anpassen
                                self.progress_bar_key_switcher.canvas.config(scrollregion=(0, 0, screenshot.width, screenshot.height))

                                # Screenshot anzeigen
                                self.progress_bar_key_switcher.photo_image = ImageTk.PhotoImage(screenshot)
                                self.progress_bar_key_switcher.canvas.delete("all")
                                self.progress_bar_key_switcher.canvas.create_image(0, 0, anchor=tk.NW, image=self.progress_bar_key_switcher.photo_image)

                                # Region zeichnen, falls vorhanden
                                if self.progress_bar_key_switcher.progress_bar_region:
                                    x, y, w, h = self.progress_bar_key_switcher.progress_bar_region
                                    self.progress_bar_key_switcher.rect_id = self.progress_bar_key_switcher.canvas.create_rectangle(
                                        x, y, x + w, y + h, outline="red", width=2
                                    )

                                # Info aktualisieren
                                if hasattr(self.progress_bar_key_switcher, 'screenshot_info_var'):
                                    self.progress_bar_key_switcher.screenshot_info_var.set(f"Screenshot geladen: {screenshot_path}")

                                self.log(f"Fortschrittsbalken-Screenshot geladen: {screenshot_path}")
                            else:
                                self.log(f"Warnung: Screenshot-Datei existiert nicht: {screenshot_path}")

                                # Prüfen, ob die Datei im debug_images-Verzeichnis existiert
                                filename = os.path.basename(screenshot_path)
                                debug_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "debug_images", filename)

                                if os.path.exists(debug_path):
                                    self.log(f"Versuche, Screenshot aus debug_images zu laden: {debug_path}")
                                    # Screenshot laden
                                    screenshot = Image.open(debug_path)
                                    screenshot_np = np.array(screenshot)
                                    screenshot_cv2 = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                                    # Screenshot speichern
                                    self.progress_bar_key_switcher.screenshot = (debug_path, screenshot, screenshot_np, screenshot_cv2)

                                    # Canvas-Größe anpassen
                                    self.progress_bar_key_switcher.canvas.config(scrollregion=(0, 0, screenshot.width, screenshot.height))

                                    # Screenshot anzeigen
                                    self.progress_bar_key_switcher.photo_image = ImageTk.PhotoImage(screenshot)
                                    self.progress_bar_key_switcher.canvas.delete("all")
                                    self.progress_bar_key_switcher.canvas.create_image(0, 0, anchor=tk.NW, image=self.progress_bar_key_switcher.photo_image)

                                    # Region zeichnen, falls vorhanden
                                    if self.progress_bar_key_switcher.progress_bar_region:
                                        x, y, w, h = self.progress_bar_key_switcher.progress_bar_region
                                        self.progress_bar_key_switcher.rect_id = self.progress_bar_key_switcher.canvas.create_rectangle(
                                            x, y, x + w, y + h, outline="red", width=2
                                        )

                                    # Info aktualisieren
                                    if hasattr(self.progress_bar_key_switcher, 'screenshot_info_var'):
                                        self.progress_bar_key_switcher.screenshot_info_var.set(f"Screenshot geladen: {debug_path}")

                                    self.log(f"Fortschrittsbalken-Screenshot aus debug_images geladen: {debug_path}")
                                else:
                                    self.log(f"Screenshot konnte nicht gefunden werden, weder im Original-Pfad noch in debug_images")
                        except Exception as e:
                            self.log(f"Fehler beim Laden des Fortschrittsbalken-Screenshots: {str(e)}")
                            import traceback
                            self.log(f"Details: {traceback.format_exc()}")

                    self.log("Fortschrittsbalken-Einstellungen wurden geladen")
                else:
                    self.log("Keine Fortschrittsbalken-Einstellungen in der Konfiguration gefunden")

                self.log(f"Konfiguration geladen: {filepath}")
            except Exception as e:
                self.log(f"Fehler beim Laden der Konfiguration: {str(e)}")
                import traceback
                self.log(f"Details: {traceback.format_exc()}")

            return filepath

        # Speicher- und Ladefunktionen ersetzen
        self.save_configuration = extended_save_configuration
        self.quick_save = extended_quick_save
        self.load_configuration = extended_load_configuration

        # Buttons aktualisieren, um die erweiterten Funktionen zu verwenden
        self.log("Aktualisiere Speicher- und Lade-Buttons...")

        # Buttons im Hauptfenster aktualisieren
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Frame):
                        for button in child.winfo_children():
                            if isinstance(button, ttk.Button):
                                if button.cget("text") == "Konfiguration speichern":
                                    button.config(command=extended_save_configuration)
                                    self.log("Speichern-Button im Hauptfenster aktualisiert")
                                elif button.cget("text") == "Schnellspeichern":
                                    button.config(command=extended_quick_save)
                                    self.log("Schnellspeichern-Button im Hauptfenster aktualisiert")
                                elif button.cget("text") == "Konfiguration laden":
                                    button.config(command=extended_load_configuration)
                                    self.log("Laden-Button im Hauptfenster aktualisiert")

        # Buttons im Einstellungen-Tab aktualisieren
        for widget in self.settings_tab.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.LabelFrame):
                        for button in child.winfo_children():
                            if isinstance(button, ttk.Button):
                                if button.cget("text") == "Konfiguration speichern":
                                    button.config(command=extended_save_configuration)
                                    self.log("Speichern-Button im Einstellungen-Tab aktualisiert")
                                elif button.cget("text") == "Schnellspeichern":
                                    button.config(command=extended_quick_save)
                                    self.log("Schnellspeichern-Button im Einstellungen-Tab aktualisiert")
                                elif button.cget("text") == "Konfiguration laden":
                                    button.config(command=extended_load_configuration)
                                    self.log("Laden-Button im Einstellungen-Tab aktualisiert")

def ensure_directories_exist():
    """
    Stellt sicher, dass alle benötigten Verzeichnisse existieren.
    """
    base_dir = os.path.dirname(os.path.abspath(__file__))

    # Liste der benötigten Verzeichnisse
    directories = [
        os.path.join(base_dir, "screenshots"),
        os.path.join(base_dir, "debug_images"),
        os.path.join(base_dir, "saves"),
        os.path.join(base_dir, "reference_images")
    ]

    # Verzeichnisse erstellen, falls sie nicht existieren
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"Verzeichnis erstellt: {directory}")
            except Exception as e:
                print(f"Fehler beim Erstellen des Verzeichnisses {directory}: {str(e)}")

def main():
    """
    Main entry point for the Macro Recorder application.
    Initializes the UI and starts the application.
    """
    # Sicherstellen, dass alle benötigten Verzeichnisse existieren
    ensure_directories_exist()

    # Create the main window
    root = tk.Tk()

    # Initialize the integrated macro recorder
    app = IntegratedMacroRecorder(root)

    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()
