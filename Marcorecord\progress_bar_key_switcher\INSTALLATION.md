# Installation und Ausführung

## Abhängigkeiten installieren

Für den Fortschrittsbalken-Tasten-Wechsler werden folgende Pakete benötigt:

```
pip install opencv-python numpy pyautogui pillow keyboard
```

<PERSON><PERSON>, dass das `keyboard`-<PERSON><PERSON> zusätz<PERSON> zu den Abhängigkeiten des Makro-Recorders benötigt wird.

## Ausführung

### Eigenständiger Modus

Um den Fortschrittsbalken-Tasten-Wechsler eigenständig auszuführen:

```
cd progress_bar_key_switcher
python main.py
```

### Integrierter Modus

Um den Fortschrittsbalken-Tasten-Wechsler in den Makro-Recorder integriert auszuführen:

```
cd progress_bar_key_switcher
python integration_example.py
```

## Verwendung

1. Starte das Programm
2. Klicke auf "Screenshot aufnehmen"
3. <PERSON><PERSON>, bis der Screenshot aufgenommen wurde (das Fenster wird kurz minimiert)
4. W<PERSON>hl<PERSON> den Bereich des Fortschrittsbalkens aus, indem du mit der Maus ein Rechteck ziehst
5. Passe bei Bedarf die Einstellungen an:
   - Fortschritts-Schwellenwert: Ab welcher Änderung wird ein Fortschritt erkannt
   - Keine-Fortschritt-Schwellenwert: Nach wie vielen Prüfungen ohne Fortschritt wird die Taste gewechselt
   - Prüfintervall: Wie oft wird der Fortschritt überprüft
6. Klicke auf "Überwachung starten"
7. Das Programm drückt automatisch die Tasten A und D, um den Fortschrittsbalken zu füllen
8. Wenn der Fortschrittsbalken fast leer ist (Ziel erreicht), stoppt die Überwachung automatisch
9. Du kannst die Überwachung jederzeit mit "Überwachung stoppen" beenden

## Fehlerbehebung

- **Problem**: Das Programm erkennt den Fortschrittsbalken nicht korrekt
  **Lösung**: Passe den Fortschritts-Schwellenwert an oder wähle einen deutlicheren Bereich des Balkens aus

- **Problem**: Das Programm wechselt zu schnell zwischen den Tasten
  **Lösung**: Erhöhe den Keine-Fortschritt-Schwellenwert oder das Prüfintervall

- **Problem**: Das Programm reagiert zu langsam auf Änderungen
  **Lösung**: Verringere das Prüfintervall
